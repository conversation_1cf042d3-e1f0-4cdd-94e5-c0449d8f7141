# IEC104转MQTT系统设计文档

## 1. 系统概述

### 1.1 项目背景
本系统旨在建立IEC60870-5-104协议与MQTT协议之间的桥梁，实现电力系统数据的现代化传输和分发。系统将IEC104协议的电力数据转换为MQTT消息，便于与云平台、IoT设备和现代监控系统集成。

### 1.2 系统目标
- 实现IEC104协议数据的实时采集
- 将采集数据转换为MQTT格式并发布
- 提供可靠的数据传输机制
- 支持多种数据类型的转换
- 提供系统监控和故障诊断功能

### 1.3 适用范围
本系统适用于：
- 牵引变电所辅助监控系统
- 电力监控数据云端传输
- 工业IoT数据集成
- 电力系统现代化改造

## 2. 系统架构

### 2.1 总体架构

系统采用网关转换架构，实现IEC104协议与MQTT协议之间的双向转换：

**数据流转路径：**
1. **IEC104设备层** → **IEC104-MQTT网关系统**：变电站RTU等IEC104设备将电力数据发送到网关
2. **IEC104-MQTT网关系统** → **MQTT Broker**：网关将IEC104数据转换为MQTT消息发布到消息代理
3. **MQTT Broker** → **MQTT客户端(监控系统)**：监控系统通过MQTT客户端订阅并接收电力数据

**管理配置路径：**
- **配置管理Web界面**：提供网关系统的配置管理界面，支持数据点映射、通信参数设置等功能

### 2.2 核心组件

#### 2.2.1 IEC104客户端模块
- **功能**：连接IEC104服务器，接收遥测、遥信数据
- **协议版本**：IEC60870-5-104
- **连接方式**：TCP/IP
- **数据类型支持**：
  - 遥测数据（模拟量）
  - 遥信数据（开关量）
  - 遥控命令
  - 对时信息

#### 2.2.2 数据转换模块
- **功能**：将IEC104数据格式转换为MQTT消息格式
- **转换规则**：可配置的映射关系
- **数据处理**：
  - 数据校验
  - 格式转换
  - 质量码处理
  - 时间戳处理

#### 2.2.3 MQTT发布模块
- **功能**：将转换后的数据发布到MQTT Broker
- **协议版本**：MQTT 3.1.1 / 5.0
- **QoS支持**：0, 1, 2
- **主题设计**：层次化主题结构

#### 2.2.4 配置管理模块
- **功能**：系统配置管理和运行监控
- **配置内容**：
  - IEC104连接参数
  - MQTT连接参数
  - 数据点映射配置
  - 系统运行参数

## 3. 数据模型设计

### 3.1 IEC104数据点类型

#### 3.1.1 遥测数据（Type ID: 13, 36）

**数据结构说明：**
- 地址字段：数据点地址标识（如1001）
- 类型字段：IEC104信息类型标识（如M_ME_NC_1表示短浮点数测量值）
- 数值字段：测量数值（如220.5）
- 质量字段：包含5个质量标识位
  - invalid：数据无效标识
  - notTopical：数据非最新标识
  - substituted：数据被替代标识
  - blocked：数据被阻塞标识
  - overflow：数据溢出标识
- 时间戳：ISO 8601格式的时间标记

#### 3.1.2 遥信数据（Type ID: 1, 30）

**数据结构说明：**
- 地址字段：数据点地址标识（如2001）
- 类型字段：IEC104信息类型标识（如M_SP_NA_1表示单点信息）
- 数值字段：布尔值（true/false）
- 质量字段：包含4个质量标识位
  - invalid：数据无效标识
  - notTopical：数据非最新标识
  - substituted：数据被替代标识
  - blocked：数据被阻塞标识
- 时间戳：ISO 8601格式的时间标记

### 3.2 MQTT消息格式

#### 3.2.1 遥测数据MQTT消息

**消息结构说明：**
- 设备标识：设备唯一编号（如RTU_001）
- 测点标识：测点唯一编号（如AI_1001）
- 测点名称：测点中文描述（如"母线电压"）
- 测量数值：实际测量值（如220.5）
- 测量单位：数值单位（如"kV"）
- 质量等级：简化质量标识（GOOD/BAD/UNCERTAIN）
- 时间戳：ISO 8601格式时间标记
- 数据类型：固定值"ANALOG"表示模拟量

#### 3.2.2 遥信数据MQTT消息

**消息结构说明：**
- 设备标识：设备唯一编号（如RTU_001）
- 测点标识：测点唯一编号（如DI_2001）
- 测点名称：测点中文描述（如"断路器状态"）
- 状态数值：状态值（1/0）
- 状态描述：状态中文描述（如"合闸"）
- 质量等级：简化质量标识（GOOD/BAD/UNCERTAIN）
- 时间戳：ISO 8601格式时间标记
- 数据类型：固定值"DIGITAL"表示数字量

### 3.3 MQTT主题设计

**主题结构规范：**
- 主题模式：/powerSystem/{stationId}/{deviceId}/{dataType}/{pointId}
- 站点标识：变电站唯一编号（如STATION_001）
- 设备标识：设备唯一编号（如RTU_001）
- 数据类型：数据分类标识（analog/digital/control/status）
- 测点标识：具体测点编号（如AI_1001）

**主题示例：**
- 模拟量数据：/powerSystem/STATION_001/RTU_001/analog/AI_1001
- 数字量数据：/powerSystem/STATION_001/RTU_001/digital/DI_2001
- 控制指令：/powerSystem/STATION_001/RTU_001/control/CO_3001
- 状态信息：/powerSystem/STATION_001/RTU_001/status/heartbeat

## 4. 技术实现方案

### 4.1 开发技术栈
- **编程语言**：Python 3.8+
- **IEC104库**：python-iec104 或 lib60870
- **MQTT库**：paho-mqtt
- **数据库**：SQLite（配置） + InfluxDB（历史数据）
- **Web框架**：Flask/FastAPI
- **前端**：Vue.js + Element UI
- **容器化**：Docker

### 4.2 核心算法

#### 4.2.1 连接管理算法

**连接管理类设计：**
- 类名：IEC104ConnectionManager
- 配置参数：包含连接配置信息
- 连接对象：维护IEC104连接实例
- 重连间隔：默认5秒重连间隔

**核心方法：**
- connect()方法：建立IEC104连接并实现重连机制
- heartbeat_check()方法：执行心跳检测功能

**实现特点：**
- 自动重连：连接断开时自动重连
- 异常处理：完善的异常捕获和处理
- 状态监控：实时监控连接状态

#### 4.2.2 数据转换算法

**数据转换类设计：**
- 类名：DataConverter
- 映射配置：包含数据点映射关系配置
- 转换方法：convert_iec104_to_mqtt()

**转换处理功能：**
- 数据格式转换：IEC104格式转换为MQTT消息格式
- 质量码映射：IEC104质量码转换为简化质量等级
- 时间戳处理：时间格式标准化处理
- 地址映射：根据配置映射数据点地址
- 数值处理：数值范围检查和格式化

### 4.3 配置文件结构

#### 4.3.1 IEC104配置

**IEC104连接配置参数：**
- 服务器地址：192.168.1.100
- 服务器端口：2404（IEC104标准端口）
- 公共地址：1（通用地址标识）
- 连接超时：30秒
- 发送参数k值：12（未确认I帧最大数量）
- 接收参数w值：8（接收后未确认I帧最大数量）
- 各项超时参数：
  - t0超时：30秒（连接建立超时）
  - t1超时：15秒（发送或测试APDU超时）
  - t2超时：10秒（无数据报文t<t2时确认超时）
  - t3超时：20秒（长期空闲状态下发送测试帧超时）

#### 4.3.2 MQTT配置

**MQTT代理连接配置参数：**
- 代理地址：mqtt.example.com
- 代理端口：1883（MQTT标准端口）
- 认证信息：
  - 用户名：gateway_user
  - 密码：gateway_pass
- 连接参数：
  - 客户端标识：iec104_gateway
  - 保活间隔：60秒
  - 服务质量：QoS=1（至少一次传递）
  - 消息保留：false（不保留消息）

#### 4.3.3 数据点映射配置

**模拟量测点映射配置：**
- IEC地址：1001（IEC104协议中的测点地址）
- MQTT主题：/powerSystem/STATION_001/RTU_001/analog/voltage_A
- 测点名称：A相电压
- 测量单位：kV（千伏）
- 缩放系数：1.0（数值转换系数）

**数字量测点映射配置：**
- IEC地址：2001（IEC104协议中的测点地址）
- MQTT主题：/powerSystem/STATION_001/RTU_001/digital/breaker_status
- 测点名称：主断路器状态
- 状态描述：
  - true对应：合闸
  - false对应：分闸

## 5. 系统安全设计

### 5.1 网络安全
- **TLS/SSL加密**：MQTT连接使用TLS 1.2+
- **证书管理**：客户端证书认证
- **防火墙规则**：限制网络访问
- **VPN连接**：安全隧道传输

### 5.2 认证授权
- **用户认证**：基于用户名密码
- **访问控制**：基于角色的权限管理
- **API安全**：JWT令牌认证
- **操作审计**：关键操作记录

### 5.3 数据安全
- **数据加密**：敏感数据加密存储
- **备份恢复**：定期数据备份
- **完整性校验**：数据传输校验
- **隐私保护**：敏感信息脱敏

## 6. 系统监控与运维

### 6.1 监控指标
- **连接状态**：IEC104和MQTT连接状态
- **数据吞吐量**：接收/发送数据量统计
- **延迟监控**：数据传输延迟
- **错误率**：通信错误率统计
- **系统资源**：CPU、内存、磁盘使用率

### 6.2 告警机制
- **连接中断告警**：通信链路异常
- **数据质量告警**：数据质量码异常
- **延迟超限告警**：数据传输延迟过高
- **系统资源告警**：资源使用率过高

### 6.3 日志管理
- **分级日志**：DEBUG、INFO、WARN、ERROR
- **日志轮转**：按大小和时间轮转
- **远程日志**：集中日志收集
- **日志分析**：异常模式识别

## 7. 部署方案

### 7.1 硬件要求
- **CPU**：4核心，2.0GHz+
- **内存**：8GB+
- **存储**：100GB SSD
- **网络**：千兆网卡
- **操作系统**：Linux Ubuntu 20.04+

### 7.2 容器化部署

**Docker镜像构建配置：**
- 基础镜像：python:3.9-slim
- 工作目录：/app
- 依赖安装：通过requirements.txt安装Python依赖包
- 应用部署：复制应用程序代码到容器
- 端口暴露：8080端口用于Web管理界面
- 启动命令：python main.py运行主程序

### 7.3 Docker Compose配置

**服务编排配置：**

**IEC104网关服务：**
- 服务名称：iec104-gateway
- 构建方式：本地构建Docker镜像
- 端口映射：主机8080端口映射到容器8080端口
- 环境变量：CONFIG_FILE指定配置文件路径
- 卷挂载：配置目录和日志目录挂载到主机
- 重启策略：unless-stopped（除非手动停止否则自动重启）

**MQTT代理服务：**
- 服务名称：mqtt-broker
- 镜像版本：eclipse-mosquitto:2.0
- 端口映射：
  - 1883端口：MQTT协议端口
  - 9001端口：WebSocket端口
- 配置挂载：mosquitto.conf配置文件挂载
- 重启策略：unless-stopped

## 8. 测试方案

### 8.1 单元测试
- **数据转换测试**：各种数据类型转换正确性
- **连接管理测试**：连接建立、断开、重连
- **配置管理测试**：配置文件读取、验证
- **质量码处理测试**：各种质量码的正确处理

### 8.2 集成测试
- **端到端测试**：从IEC104到MQTT的完整流程
- **性能测试**：大量数据点的处理能力
- **压力测试**：高频数据更新的稳定性
- **故障恢复测试**：各种故障场景的恢复能力

### 8.3 测试环境
- **IEC104模拟器**：模拟变电站RTU设备
- **MQTT测试客户端**：验证消息发布
- **网络模拟**：模拟各种网络条件
- **性能监控**：测试过程中的系统监控

## 9. 维护升级

### 9.1 版本管理
- **语义化版本**：遵循SemVer规范
- **发布流程**：开发→测试→预发布→生产
- **回滚策略**：快速回滚机制
- **兼容性管理**：向后兼容性保证

### 9.2 升级策略
- **在线升级**：不停机升级能力
- **配置迁移**：配置文件自动迁移
- **数据迁移**：历史数据迁移
- **功能开关**：新功能灰度发布

## 10. 附录

### 10.1 相关标准
- **IEC 60870-5-104**：传输协议标准
- **MQTT 3.1.1/5.0**：消息传输协议
- **IEEE 1815**：DNP3协议（可扩展支持）
- **IEC 61850**：变电站通信协议（可扩展支持）

### 10.2 参考资料
- IEC60870-5-104协议规范
- MQTT协议规范
- 牵引变电所辅助监控系统技术规范
- 工业物联网安全指南

### 10.3 术语表
- **RTU**：Remote Terminal Unit，远程终端单元
- **ASDU**：Application Service Data Unit，应用服务数据单元
- **APCI**：Application Protocol Control Information，应用协议控制信息
- **QoS**：Quality of Service，服务质量
- **TLS**：Transport Layer Security，传输层安全

---

**文档版本**：V1.0  
**创建日期**：2025-05-25  
**最后更新**：2025-05-25  
**文档状态**：初稿
