4　系统构成
4.1　辅助监控系统采用分层架构，由站控层和间隔层组成，站控层网络采用星型以太网组网，系统构成见图1。
 
   标引序号说明：
    1——采用综合视频监控系统，视频监控及巡检子系统的视频监控部分不设置，由综合视频监控系统实现；
    2——满足消防要求时可接入火灾自动报警系统；
    3——网络安全保护符合要求时，可与综合自动化系统连接；
    4——安全防范子系统、环境监测子系统、火灾报警子系统、动力照明控制子系统支持通信的设备，可直接接入网络；

图1　辅助监控系统架构图
4.2　辅助监控系统包含视频监控及巡检、安全防范、环境监测、火灾报警、动力照明控制等子系统。
4.3　辅助监控系统站控层应包括综合应用服务器、站级终端、通信管理机、视频服务器和网络设备等，间隔层应包含子系统各辅助设备和动力环境测控装置等。
4.4　视频监控及巡检、安全防范、环境监测、火灾报警、动力照明控制等各间隔层设备应满足各自产品标准的要求。
4.5　辅助监控系统应满足与辅助监控系统主站、火灾自动报警系统、综合自动化系统、国铁集团数据服务平台等的接口要求。
4.6　辅助监控系统通信通道要求应满足Q/CR 796-2020 第中6.3.1.6条的相关规定。
5　系统功能及性能
5.1　主要功能要求
5.1.1　采集功能
系统应能采集布置在所内的辅助设备信息，主要包括以下内容：
a)	视频监控及巡检信息，
a)	采集实时视频流数据、巡检照片、语音对话、自检信息、告警信息等。；
b)	安全防范信息，
b)	采集所内门禁、红外双鉴/三鉴、激光/红外对射、玻璃破碎等告警信息、自检信息等。；
c)	火灾报警信息，
c)	采集火灾处理单元的现场探测信息、设备自检信息等。；
d)	环境监控信息：
1)	采集温湿度传感器、水浸传感器、SF6气体监测传感器、空调等设备运行信息、告警信息及自检信息；
2)	采集环境温度、湿度、风力、雨量等气象信息。
e)	动力照明设备，
e)	采集灯光控制器、水泵、风机等动力照明设备的运行信息。
5.1.2　视频监控及巡检
5.1.2.1　通用要求
视频监控及巡检应具有视频显示、图像存储与回放、视频设备控制、视频巡检、智能图像识别、红外热成像监测等功能。
5.1.2.2　视频显示
系统视频显示主要包括以下内容：
a)	实时图像监视，视频图像大小可调；
b)	支持VGA/HDMI、主辅音视频及辅助视频端口的本地输出；
c)	支持1/4/9/16画面预览，预览通道顺序可调；
d)	支持预览分组切换、手动切换或自动轮巡预览，自动轮巡周期可设置；
e)	支持预览的电子放大；
f)	可屏蔽指定的预览通道；
g)	支持视频移动侦测、视频丢失检测、视频遮挡检测、视频输入异常检测；
h)	支持视频隐私遮盖；
i)	支持多种主流云台解码器控制协议，支持预置点、巡航路径及轨迹；
j)	云台控制时，支持鼠标点击放大、鼠标拖动跟踪功能；
k)	支持慢放、快放、倒放及逐帧播放等多种回放模式，时间进度条可拖动定位。
5.1.2.3　图像存储及回放
系统图像存储及回放主要包括以下内容：
a)	支持循环写入和非循环写入两种模式；
b)	每个通道的视频编码参数独立可调，包括分辨率、帧率、码率、图像质量等，支持定时和事件两套压缩参数；
c)	支持硬盘配额和硬盘盘组两种存储模式，可对不同通道分配不同的录像保存容量或周期；
d)	录像触发模式包括手动触发、定时触发、报警信号触发、移动侦测信号触发、动力环境测控装置信号触发等多种模式；
e)	每天可设定8个以上录像时间段，不同时间段的录像触发模式可独立设置；
f)	支持按事件查询录像文件；
g)	支持录像文件的锁定和解锁；
h)	支持指定硬盘内的录像资料只读属性设置，录像资料仅供读取；
i)	支持按通道号、录像类型、文件类型、起止时间等条件进行录像资料的检索和回放和文件存储；
j)	支持回放时对任意区域进行局部电子放大；
k)	支持回放时的暂停、快放、慢放、前跳、后跳，支持鼠标拖动定位。
5.1.2.4　视频设备控制
系统视频设备控制主要包括以下内容：
a)	具备完整控制所内视频前端设备的功能，实现对摄像机视角、方位、焦距、光圈、景深的调整，对于带预置点云台，操作人员能直接进行云台的预置和操作。可设置摄像机参数，包括设置预置位、区域名称、区域遮盖等。摄像机的全景设备画面应设置为预置位1，并默认为摄像机的守望位；
b)	支持操作人员权限和优先级管理，保证控制的唯一性。当高优先级用户操作视频设备时，低优先级用户不能控制此设备；当相同优先级的用户控制视频设备时，先申请控制权的用户获得控制权；当低优先级用户已获得视频设备的控制权时，高优先级用户可抢夺获取此视频设备的控制权；辅助监控系统摄像机控制权限的优先级划分如应符合表1的规定所示，数字越小控制优先级越高；
c)	可完整的远程控制前端设备：如现场云台、电动变焦镜头、防护罩雨刷等各种受控设备，云台应具备预置点、巡航路径及轨迹功能，操作人员能直接进行云台预置点的设置和调用；
d)	可进行当地或远程布防/撤防，也可以事先确定布防/撤防策略，由系统按照制定的策略自动进行布防/撤防；也可以通过电子地图进行布防或者撤防；
e)	应具备自动联动功能，当辅助监控系统或供电设备产生告警，或者对设备进行远程操作时，附近摄像机应自动对准报警点或相关设备，并进行录像；
f)	应具备辅助监控系统段级主站远程设置摄像机预置位的功能；
g)	辅助监控系统局级主站、段级主站、牵引变电所辅助监控系统应通过鉴权中心申请/释放视频设备的控制权，鉴权中心宜设置在辅助监控系统段级主站。
表1辅助监控系统摄像机控制权限的优先级定义表
权限等级	控制或触发条件	控制权申请层级	说明
第一级	所亭内火灾报警、SF6泄漏及入侵信号
视频联动	所亭	牵引变电所辅助监控系统报警联动
第二级	故障跳闸信号视频联动	局集团公司	SCADA报警联动
第三级	开关非远动动作及重要故障遥信信号视频联动（例：PT断线、综自系统远动通信异常等信号）	局集团公司	SCADA报警联动
第四级	遥控操作视频联动	局集团公司	SCADA遥控联动
第五级	国铁集团用户手动调取视频画面	国铁集团	国铁集团手动控制
第六级	局级用户手动调取视频画面	局集团公司	SCADA遥控联动
第七级	段级用户手动调取视频画面	业务段	段级手动控制
第八级	车间级用户手动调取视频画面	车间	车间级手动控制
第九级	所亭内动力环境及安防普通报警信号视频联动
（例：水浸传感器报警、玻璃破碎等信号）	所亭	牵引变电所辅助监控系统报警联动
第十级	一般遥信故障信号视频联动
（例：主变油位异常、主变温度过高、断路器弹簧未储能、SF6压力低等信号、保护装置通信异常等信号）	局集团公司	SCADA报警联动
第十一级	所亭内用户手动调取视频画面	所亭	所级手动控制
第十二级	所亭内视频巡检	所亭	牵引变电所辅助监控系统的视频巡检
5.1.2.5　视频巡检
系统视频巡检主要包括以下内容：
a)	应能接收辅助监控系统主站下发的巡检卡片，实现对牵引变电所的远程智能巡视，能够替代人工巡视；
b)	应具备对不同区域的巡检卡片并发执行的功能；
c)	视频巡检过程中出现异常信息时及时弹出，此时巡检不中断；
d)	 应具备高优先级的视频调用结束后自动从巡检中断处恢复巡检的功能；
e)	视频巡检支持在固定时间自动启动，也可由操作员手动启动；
f)	应具备巡检策略管理功能，可根据所内实际设备情况对巡视线路、巡视步骤、巡视对象、巡视项目等内容进行自定义；
g)	可设置每个摄像机的预置位置、停留时间、是否拍照；
h)	应具有巡检对象的标准图谱数据库，具备利用智能图像识别技术，在自动视频巡检时判断设备运行是否正常，如果出现异常自动进行报警的功能；
i)	应具备巡检记录功能，巡检结束自动生成巡检记录表，并将巡检记录上传给辅助监控系统主站。巡检记录表能按照巡检时期、巡检设备、巡检人员等条件进行历史记录查询；
j)	视频巡检宜采用固定摄像机，也可采用导轨式摄像机。
5.1.2.6　图像识别
系统智能图像识别主要包括以下内容：
a)	应能对牵引变压器油位刻度进行智能识别和判断，在异常时进行报警；
b)	应能对户外高压断路器的分/合指示牌进行智能识别，判断开关状态；
c)	应能对隔离开关运行状态进行视频图像智能分析，实时判断隔离开关分合状态，并实现异常报警；
d)	应能对户内高压开关柜、控制保护屏、交直流屏分合指示牌、转换开关、压板位置、指示灯进行视频图像智能分析，实时判断设备状态；
e)	应能对避雷器动作计数器智能图像识别；
f)	应能对其他各类数字式、指针式仪表的视频图像进行智能分析，实时判断仪表读数并智能报警；
g)	应能对安全防范子系统上送的报警进行图像复核，确认是否为真实有效的报警，以解决传统周界防范等系统误报率高的问题。当相关报警信号产生时，应将摄像机对准报警发生地，并采集实时图像，进行智能识别，经图像复核实现智能报警；
h)	应能对巡检设备应能进行智能识别，判断出设备以下状态：
1)	外观是否完好，是否倾斜，是否有破损；
2)	外观是否膨胀，是否有变形、开裂；
3)	设备是否渗油、漏油；
4)	设备是否冒烟，设备是否起火。
i)	 应能对智能图像识别出的数字类信号，如油位、气体压力、油温等信息应按遥测量上送辅助监控系统主站。
5.1.2.7　红外热成像监测
系统红外热成像监测主要包含以下内容：
a)	红外热像仪应能对高压进线、变压器等一次系统主导流设备进行红外温度监视； 
b)	红外热像仪应能对变压器、互感器、避雷器、导线、线夹等设备的热像图谱进行分析。采集到设备的红外图像后，采取表面温度判断法、同类比较判断法、图像特征判断法、历史数据比较法等方法，来判断设备的状态，如果达到警戒值则进行报警；
c)	红外测温的频度应满足对一次设备主导流回路温度监测的需要；红外测温的温升应有环境温度的参考值；
d)	温度监测宜采用测温巡检监测、周期轮巡监测、守望实时监测等多种方式实现，温度监测数据宜通过遥测方式上送主站；
e)	测温巡检监测时，巡检时间宜设置在落日后或夜间，以减少阳光照射引起的误差；
f)	周期轮巡监测时，轮巡的频度、预置位停留时间、同一预置位温度读取次数可调，建议轮询周期不小于4小时h；
g)	红外热成像摄像机视场角较小，只能覆盖部分设备，守望实时监测时，守望位设置宜关注关键设备点。
5.1.3　安全防范及门禁管理
系统安全防范及门禁管理主要包含以下内容：
a)	通过各种探测和传感技术的综合应用，针对牵引变电所周界场所，实现在入侵破坏前的预警作用；
b)	对围墙、大门、窗户进行监视和入侵探测，对非法侵入进行提示警告，以保障变电所场地及周边环境的安全；
c)	实时展示安防设备的工作状态、告警状态，可对安防设备进行布防和撤防；
d)	门禁系统支持远方控制及音频、视频对话、权限设定等功能，宜具备人脸、指纹、密码等多种认证方式。应能从内部手动解锁，支持紧急情况下的钥匙解锁功能。
5.1.4　环境监测
系统环境主要包含以下内容：
a)	能对所内的温度、湿度、风力、水浸、SF6气体浓度等环境信息进行实时采集、处理和上传，采集周期不大于5秒s；
b)	可设置不同级别的环境信息告警值；
c)	支持环境量实时数据的可视化展示，可设置报警上下限阈值；
d)	环境信息历史数据应能保存，存储周期为10秒s至10分钟min可调。
5.1.5　报警
系统报警主要包含以下内容：
a)	报警类别：安防及门禁报警、环境信息报警、火灾报警、巡检报警。报警可根据需要进行分级，分为Ⅰ、Ⅱ、Ⅲ级，报警信息级别划分见附录A，报警信号、报警内容可在任何画面自动显示。当发生报警时，视频服务器能自动进行存盘录像；
b)	系统应具备处理多事件多点报警的能力，多点报警时采用弹窗方式提示，报警信息不得丢失和误报；
c)	各种探测器可以手动、自动进行布防、撤防，并可按时间自动投入或自动撤防；
d)	报警视窗内提供报警信号的详细信息，可以通过点击报警信息切换报警画面；
e)	报警联动录像具备长延时录像和慢速回放功能；
f)	可方便通过弹出菜单设置报警联动的摄像机，可以联动智能摄像机的不同预置位；
g)	报警信息可以区分该报警信息是否已被用户检查确认；
h)	当满足消防要求接入火灾自动报警系统时，视频监控系统应具有与火灾自动报警系统联动的功能，即当发生火灾时，火灾发生点处附近的摄像机应能跟踪拍摄火灾情况，同时，彩色监视器画面应能自动切换，以显示火灾情况；
i)	报警信息具备存储管理及报表统计功能，所有报警信息均可查询，并可打印输出。
5.1.6　控制
系统控制主要包含以下内容：
a)	应能对照明、风机、水泵、空调、门禁、摄像机等设备进行远方控制；
b)	应具备对控制室内的空调温度进行远方调节的功能。
5.1.7　联动
5.1.7　	系统联动主要包含以下内容：
a)	辅助设备之间的联动关系应支持用户自定义；
b)	当入侵行为触发报警时（围禁、门禁告警、私自破坏门禁设备等），相关摄像机自动凝视侵入目标并启动录像功能，启动声光报警器，夜间自动打开室外照明；
c)	室内温湿度越限时，监控界面自动给出报警信息，并启动风机或空调进行调节；
d)	发生火灾报警时，联动火灾发生区域的摄像机进行录像，关闭空调、风机，自动打开所有门禁，并启动声光报警；
e)	当水浸探测报警时，能自动启动水泵系统；
f)	当SF6探测器报警时，能自动启动风机系统；
g)	与SCADA系统进行联动。在操作开关设备或发生事故跳闸时应能联动周围的摄像机，自动将摄像机对准到相关设备，实现多角度视频信息的实时监控，通过智能图像识别实现信息复核，并对整个操作过程进行全程录像；
h)	照明控制系统与视频监视系统、火灾报警系统、安全防范系统等多个系统应实现联动，实现夜间和光线照度不够时提供足够的光线亮度。
5.1.8　电子地图
系统电子地图主要包含以下内容：
a)	具有电子地图功能，可以方便地导入JPEG、BMP格式图片；
b)	支持在图片上添加各种设备图元，支持设备图元与实际设备一一对应；
c)	在电子地图上可实现环境信息、安防信息、火灾报警信息的查看及相关设备控制；
d)	在电子地图上具有相应告警提示功能。
5.1.9　数据存储与上传
系统数据存储与上传主要包含以下内容：
a)	系统支持变电所视频信息、环境信息、设备状态、告警信息等各类数据的存储。所有的历史数据均可查询，可支持按照时间、设备对象、事件等多种条件或线索组合进行查询；
b)	历史数据可以曲线的形式展示，支持在同一个窗口上显示多条曲线；
c)	曲线条数、曲线展示风格、报警阈值的上下限、曲线扫描周期等参数可设；
d)	历史数据支持以Excel、TXT文件格式进行导出备份，支持输出成报表，并可输出打印；
e)	辅助监控系统应将音视频信息和数据信息上传至辅助监控系统局级主站和辅助监控系统段级主站，数据信息的分类和信息流向见附录A。音视频信息和数据信息全部存储于牵引变电所，数据允许有权限的管理人员拷贝及远方召唤；
f)	辅助监控系统历史数据当地存储时间要求：
1）巡检记录存储时间不小于1年；
2）报警信息存储时间不小于1年；
3）巡检照片存储时间不小于1年；
4）监控视频存储时间不小于3个月；
5）其他数据存储时间不小于1年。
5.1.10　平台展示和报表
系统平台展示和报表主要包含以下内容：
a)	应能以平面图、接线图、列表等多种画面方式实时展示辅助设备的运行状态、数据信息、告警提示等；
b)	应能在画面上直接控制可控的辅助设备；
c)	应能实现告警联动、操作联动的展示；
d)	应能以多画面或轮巡的方式展示实时视频，并对实时视频进行录像和抓图；
e)	应支持各种报表类型，包括日报、月报、年报及自定义的报表，具有对报表的调用、显示及打印等功能。
5.1.11　对时
辅助监控系统应具有自动对时功能，时间采集源可为辅助监控系统主站授时或所内统一的授时时钟。当采用辅助监控系统主站授时时，可采用NTP协议或DL/T 634.5104规约。
5.1.12　自检功能
辅助监控系统应具备自检功能，并满足如下要求：
a)	自检对象包括但不限于：
——1)站控层设备、间隔层设备的通信状态；
2)——视频服务器的硬盘满、硬盘故障、非法访问等状态；
3)——摄像机的离线、遮挡等状态；
4)——安全防范子系统各类探测器、控制器的通信状态；
5)——环境监测子系统各类传感器、探测器的通信状态；
6)——火灾报警子系统各类探测器、控制器的通信状态；
7)——动力照明控制子系统各类控制器的通信状态。
b)	应具备将上述自检信息以遥信信号的方式上送辅助监控系统主站的功能。
5.1.13　远程通信
辅助监控系统应具有与辅助监控系统主站进行远程通信功能，并满足如下要求：
a)	应具备向辅助监控系统主站上送遥控/故障联动及手动召唤的辅助监控图像信息、辅助监控数值型信息和应急音频信息的功能；
b)	应具备接收辅助监控系统主站下发的视频前端设备的控制命令的功能；
c)	应具备接收辅助监控系统主站下发的控制命令、数据召唤命令及巡检计划、巡检任务等数据的功能；
d)	应具备向辅助监控系统主站上送巡检图像、音频、数值型数据的功能；
e)	可具备向辅助监控系统主站上送在线监测数据的功能；
f)	应具备多主站远程通信功能，至少应支持与两套辅助监控系统段级主站、两套辅助监控系统局级主站进行远程通信的能力；
g)	应具备检修勿扰主站功能，当动力环境测控装置的软压板“检修”处于投入状态时，通信管理机接收到该装置的遥测、遥信（除检修状态）不上送主站。
5.1.14　其它要求
辅助监控系统应满足如下要求：
a)	牵引变电所辅助监控系统的软件界面要求应符合见附录B的规定；
b)	辅助监控系统局级主站、段级主站应满足Q/CR 796-2020的要求，软件界面要求应符合见附录C的规定；
c)	辅助监控系统登陆界面不应使用自动登陆，口令不应使用弱口令。
5.2　性能要求
5.2.1　可用性指标
辅助监控系统可用性指标如下：
a)	可用率：≥99.99%；
b)	系统平均无故障工作时间： MTBF≥30000 h；
c)	硬件平均无故障工作时间： MTBF≥50000 h。
5.2.2　实时性指标
辅助监控系统实时性指标如下：
a)	视频控制切换响应时间：≤1 s；
b)	控制响应时间：≤1 s；
c)	子站监控画面显示与实际事件发生时间差：≤1 s
d)	事件报警到系统自动记录相应画面时间差：≤1 s；
e)	各报警探头报警到所内综合应用服务器信息显示时间差：≤1 s；
f)	系统时钟精度：≤10 ms。
5.2.3　图像指标
辅助监控系统图像指标如下：
a)	图像存储质量：不低于CIF格式（352×288）；
b)	图像压缩质量：不低于CIF格式（352×288）；
c)	视频信号制式：PAL或NTSC；
d)	图像传输速度：不小于25帧/秒s。
5.2.4　性能指标
辅助监控系统主要性能指标如下：
a)	SOE分辨率：：≤2ms；
b)	测量量：综合误差：≤0.5%，；遥测合格率：≥99.99%；
c)	状态量：遥信响应率100%；
d)	遥控：遥控正确率100%。
