# 变电所辅助监控系统详细设计文档

## 1. 项目概述

### 1.1 项目背景
变电所辅助监控系统是一套集成化的智能监控平台，基于Q/CR 1029-2024《牵引变电所辅助监控系统技术规范》要求，实现对变电所内视频监控及巡检、安全防范、环境监测、火灾报警、动力照明控制等功能的高度集成和一体化监控。系统采用分层架构，由站控层和间隔层组成，站控层网络采用星型以太网组网。

### 1.2 设计目标
- **标准化架构**：遵循Q/CR 796-2020和Q/CR 1029-2024等行业标准
- **高度集成**：实现视频监控及巡检、安全防范、环境监测、火灾报警、动力照明控制等子系统的统一监控
- **智能化水平**：具备智能图像识别、自动巡检、智能联动等功能
- **高可用性**：系统可用率≥99.99%，MTBF≥30000h
- **实时性保证**：视频控制切换响应时间≤1s，控制响应时间≤1s

### 1.3 系统组成
系统包含以下主要子系统：
- **视频监控及巡检子系统**：视频显示、图像存储回放、视频设备控制、智能图像识别、红外热成像监测
- **安全防范及门禁子系统**：周界防护、入侵检测、门禁管理
- **环境监测子系统**：温湿度、水浸、SF6气体、气象信息监测
- **火灾报警子系统**：火灾探测、报警处理、消防联动
- **动力照明控制子系统**：照明、风机、水泵、空调等设备控制

### 1.4 适用范围
本系统适用于：
- 牵引变电所辅助监控
- 配电站综合监控
- 工业变电站智能监控
- 电力系统现代化改造项目

## 2. 系统总体架构

### 2.1 架构设计原则
- **分层架构**：采用站控层和间隔层的两层架构设计
- **服务化架构**：核心功能模块化，各服务独立部署
- **中心化配置**：所有配置数据统一由后台服务管理
- **消息驱动**：基于Redis消息中间件实现服务间通信
- **标准协议**：对外采用IEC104标准协议，内部采用HTTP/HTTPS

### 2.2 系统整体架构

系统采用分层架构设计，从上到下分为以下层次：

**客户端层：**
- Qt客户端(桌面)：提供完整的监控功能界面，支持本地部署
- Web配置管理端(Vue.js)：基于浏览器的配置管理界面


客户端层通过HTTP/HTTPS和WebSocket协议与服务层进行通信。

**服务层：**
服务层的核心是后台服务(ASP.NET Web API)，负责：
- 配置管理API：统一配置数据管理
- 数据管理API：实时数据处理
- 用户权限API：身份认证和权限控制
- 系统管理API：系统运行状态管理
- 对外HTTPS接口：主站通信接口
- 内部HTTP接口：服务间通信接口

后台服务通过HTTP协议和Redis消息队列与其他业务服务通信：
- 数据服务(WPF)：包含IEC104服务端，负责数据采集和协议转换
- 巡检服务(Qt C++)：自动巡检任务执行
- 识别服务(C++)：AI图像识别处理

**数据层：**
- 配置数据库(MySQL)：存储系统配置信息
- 历史数据库(MongoDB)：存储历史运行数据
- 实时数据库(Redis)：缓存实时数据和消息队列

**设备层：**
包含现场设备和控制设备：
- 通信管理机：设备通信接口
- NVR：网络视频录像机
- 传感器设备：环境监测传感器
- 摄像头：视频监控设备
- 控制设备：动力照明控制设备
- 监测设备：各类监测装置


### 2.3 架构核心特点

#### 2.3.1 中心化配置管理
- **统一配置源**：所有系统配置数据由后台服务统一管理
- **配置分发**：各业务服务从后台服务获取所需配置

#### 2.3.2 服务间通信机制
- **后台服务**：作为系统核心，负责配置管理和外部通信
- **数据服务**：从后台服务获取数据点配置，对外提供IEC104服务
- **巡检服务**：从后台服务获取巡检配置，上传巡检结果
- **识别服务**：从后台服务获取识别参数，提供AI识别能力

#### 2.3.3 分层网络架构
- **站控层**：综合应用服务器、站级终端、通信管理机、视频服务器
- **间隔层**：各子系统辅助设备和动力环境测控装置
- **网络拓扑**：站控层采用星型以太网组网

### 2.4 核心模块架构

#### 2.4.1 后台服务架构 (系统核心)

后台服务采用ASP.NET Web API架构，作为系统的核心组件，分为以下层次：

**外部接口层：**
- HTTPS接口：用于主站通信，提供安全的对外数据交换
- IEC104接口：符合电力行业标准的主站通信协议
- WebSocket：实现实时数据推送功能
- FTP接口：用于文件传输和数据交换

**内部接口层：**
- HTTP接口：服务间通信的基础协议
- RESTful API：标准化的API设计模式

**业务服务层：**
- 配置管理：系统配置参数的统一管理
- 用户管理：用户账户和身份认证
- 设备管理：设备信息和状态管理
- 数据管理：实时和历史数据处理
- 权限管理：基于角色的访问控制
- 报警管理：事件和报警处理
- 统计分析：数据统计和分析功能
- 文件管理：文件上传下载和存储

**数据访问层：**
- 配置数据访问：配置数据库的数据访问接口
- 用户数据访问：用户相关数据的访问接口
- 设备数据访问：设备信息数据的访问接口
- 历史数据访问：历史数据库的访问接口

**缓存层：**
- Redis缓存：分布式缓存服务
- 内存缓存：本地内存缓存
- 分布式缓存：集群环境下的缓存同步
- 会话缓存：用户会话状态管理

#### 2.4.2 数据服务架构

数据服务采用WPF技术实现，作为数据采集和协议转换的核心组件，分为以下层次：

**IEC104服务层：**
- IEC104服务端：实现标准IEC104协议服务
- 协议解析：处理协议帧格式和数据解析
- 数据转换：实现数据格式转换和标准化
- 状态管理：维护连接状态和通信状态

**配置获取层：**
- HTTP客户端：与后台服务进行通信
- 配置同步：从后台服务获取最新配置
- 热更新：支持配置在线更新无需重启
- 版本控制：配置版本管理和变更跟踪

**数据处理层：**
- 数据采集：从现场设备采集实时数据
- 数据转换：原始数据转换为标准格式
- 数据校验：数据有效性检查和异常处理
- 数据缓存：数据缓存和快速访问
- 实时处理：实时数据流处理
- 历史存储：历史数据归档存储
- 报警判断：实时报警条件判断
- 联动控制：报警联动控制逻辑

**消息分发层：**
- Redis发布者：向Redis发布实时数据
- 消息队列：可靠的消息传递机制
- 发布订阅：事件驱动的消息分发
- 广播推送：向多个订阅者推送数据

**设备通信层：**
- 通信管理机接口：与通信管理机的通信接口
- Modbus客户端：Modbus协议设备通信
- OPC UA客户端：OPC UA协议设备通信
- 自定义协议：支持厂家自定义通信协议

#### 2.4.3 巡检服务架构

巡检服务采用Qt C++技术实现，专门负责自动巡检任务的执行，分为以下层次：

**任务管理层：**
- 任务调度：根据配置的时间计划自动触发巡检任务
- 任务执行：执行具体的巡检流程和步骤
- 任务监控：监控任务执行状态和进度
- 结果上报：将巡检结果上报给后台服务

**配置获取层：**
- HTTP客户端：与后台服务进行通信
- 配置同步：获取巡检任务配置和参数
- 定时更新：定期更新配置确保时效性
- 缓存管理：本地配置缓存和管理

**设备控制层：**
- 摄像头控制：控制摄像头的开关和参数设置
- 云台控制：控制云台的转动和定位
- 预置位调用：调用预设的摄像头位置
- 录像控制：控制录像的开始和停止

**图像处理层：**
- 图像采集：从摄像头采集巡检图像
- 视频编码：对视频流进行编码处理
- 图像识别调用：调用AI识别服务进行分析
- 结果分析：分析识别结果并生成报告

**通信接口层：**
- NVR接口：与网络录像机的通信接口
- 摄像头接口：与摄像头设备的直接通信
- 识别服务接口：与AI识别服务的通信接口
- Redis接口：与消息中间件的通信接口

#### 2.4.4 Web配置管理端架构

Web配置管理端采用Vue.js技术实现，提供基于浏览器的配置管理界面，分为以下层次：

**表示层：**
- 用户界面：提供直观的用户操作界面
- 表单组件：各种配置表单和输入组件
- 图表组件：数据可视化展示组件
- 地图组件：电子地图和地理信息展示

**业务逻辑层：**
- 配置管理：系统各项配置参数的管理功能
- 用户管理：用户账户和权限的管理功能
- 设备管理：设备信息和状态的管理功能
- 权限管理：基于角色的权限分配和控制
**数据访问层：**
- HTTP客户端：与后台服务进行通信
- API调用：调用后台服务提供的RESTful API
- 数据缓存：本地数据缓存和状态管理
- 状态管理：全局状态管理和数据同步

**工具层：**
- 路由管理：前端路由配置和导航管理
- 权限控制：基于角色的界面权限控制
- 错误处理：统一的错误处理和提示机制
- 工具函数：通用工具函数和助手方法

## 3. 数据架构设计

### 3.1 数据分层架构

#### 3.1.1 数据存储架构

系统采用分层数据存储架构，根据数据特性和访问需求分为三个数据层：

**实时数据层 (Redis)：**
- 实时遥测数据：当前时刻的模拟量数据
- 实时遥信数据：当前时刻的开关量数据
- 设备在线状态：设备通信状态和健康状态
- 报警信息：实时报警事件和状态
- 会话数据：用户登录会话和状态信息
- 消息队列：系统内部消息传递和事件通知

**历史数据层 (MongoDB)：**
- 历史遥测数据：按时间序列存储的历史模拟量数据
- 历史遥信数据：按时间序列存储的历史开关量数据
- 操作记录：用户操作历史和系统变更记录
- 报警历史：历史报警事件和处理记录
- 巡检记录：自动巡检任务执行历史和结果
- 视频文件信息：录像文件元数据和存储路径信息

**配置数据层 (MySQL)：**
- 设备配置信息：设备基本信息和参数配置
- 测点配置信息：数据点配置和映射关系
- 用户权限信息：用户账户、角色和权限配置
- 系统参数配置：系统运行参数和全局配置
- 通信参数配置：通信协议和连接参数
- 映射关系配置：设备与测点的映射关系配置

#### 3.1.2 数据流转架构

系统数据流转采用分层处理架构，数据从现场设备到最终应用经过以下流程：

**数据采集流程：**
1. 现场设备 → 通信管理机：设备将原始数据通过标准协议发送给通信管理机
2. 通信管理机 → 数据服务：通信管理机对数据进行协议解析和初步处理
3. 数据服务 → Redis消息：数据服务进行数据处理后发布到Redis消息队列
4. Redis消息 → 客户端/后台服务：各业务服务订阅消息队列获取实时数据
5. 客户端/后台服务：进行最终的业务处理和界面展示

**数据处理层次：**
- 设备层：原始数据产生层，包含各种传感器和控制设备
- 采集层：协议解析层，将设备协议转换为标准格式
- 处理层：数据处理层，进行数据校验、转换和存储
- 分发层：消息分发层，实现数据的实时分发和推送
- 应用层：业务处理层，提供最终的应用功能和用户界面

**配置数据流向：**
配置数据采用中心化管理模式：
- 后台服务(配置管理) → HTTP接口：配置数据通过HTTP接口分发
- HTTP接口 → 各业务服务：各业务服务通过HTTP获取所需配置
- 各业务服务 → 本地缓存：业务服务将配置缓存到本地以提高访问性能

### 3.2 数据模型设计

#### 3.2.1 遥测数据模型

| 字段名称 | 数据类型 | 长度 | 必填 | 说明 | 示例值 |
|----------|----------|------|------|------|--------|
| 测点编号 | String | 20 | 是 | 遥测点唯一标识 | AI_001_001 |
| 设备编号 | String | 20 | 是 | 所属设备编号 | RTU_001 |
| 测点类型 | String | 10 | 是 | 固定值"模拟量" | 模拟量 |
| 测点名称 | String | 50 | 是 | 测点中文名称 | 10kV母线电压 |
| 测点值 | Double | - | 是 | 当前数值 | 220.5 |
| 数据质量 | String | 10 | 是 | 质量标识 | 好 |
| 时间戳 | DateTime | - | 是 | 数据时间戳 | 2025-05-31T10:30:00.000Z |
| 单位 | String | 10 | 否 | 工程单位 | kV |
| 上限值 | Double | - | 否 | 上限阈值 | 230.0 |
| 下限值 | Double | - | 否 | 下限阈值 | 200.0 |
| 报警等级 | String | 10 | 否 | 当前报警等级 | 无 |

#### 3.2.2 遥信数据模型

| 字段名称 | 数据类型 | 长度 | 必填 | 说明 | 示例值 |
|----------|----------|------|------|------|--------|
| 测点编号 | String | 20 | 是 | 遥信点唯一标识 | DI_002_001 |
| 设备编号 | String | 20 | 是 | 所属设备编号 | RTU_001 |
| 测点类型 | String | 10 | 是 | 固定值"开关量" | 开关量 |
| 测点名称 | String | 50 | 是 | 测点中文名称 | 主断路器位置 |
| 测点值 | Integer | - | 是 | 0或1 | 1 |
| 数据质量 | String | 10 | 是 | 质量标识 | 好 |
| 时间戳 | DateTime | - | 是 | 数据时间戳 | 2025-05-31T10:30:00.000Z |
| 状态描述 | String | 20 | 否 | 状态描述 | 合位 |
| 变位标志 | Boolean | - | 否 | 是否发生变位 | false |
| 报警标志 | Boolean | - | 否 | 是否有报警 | false |

#### 3.2.3 设备状态模型

| 字段名称 | 数据类型 | 长度 | 必填 | 说明 | 示例值 |
|----------|----------|------|------|------|--------|
| 设备编号 | String | 20 | 是 | 设备唯一标识 | RTU_001 |
| 设备名称 | String | 50 | 是 | 设备中文名称 | 主变保护装置 |
| 设备类型 | String | 20 | 是 | 设备分类 | 保护装置 |
| 通信状态 | String | 10 | 是 | 在线/离线 | 在线 |
| 运行状态 | String | 10 | 是 | 正常/故障 | 正常 |
| 最后通信时间 | DateTime | - | 是 | 最近通信时间 | 2025-05-31T10:30:00.000Z |
| 故障信息 | String | 200 | 否 | 故障描述 | "" |
| 测点总数 | Integer | - | 是 | 测点总数量 | 128 |
| 异常测点数 | Integer | - | 是 | 异常测点数量 | 0 |

#### 3.2.4 报警信息模型

| 字段名称 | 数据类型 | 长度 | 必填 | 说明 | 示例值 |
|----------|----------|------|------|------|--------|
| 报警编号 | String | 30 | 是 | 报警唯一标识 | ALM_20250531_001 |
| 设备编号 | String | 20 | 是 | 所属设备编号 | RTU_001 |
| 测点编号 | String | 20 | 是 | 相关测点编号 | AI_001_001 |
| 报警类型 | String | 20 | 是 | 报警分类 | 越上限 |
| 报警等级 | String | 10 | 是 | 重要/一般/提示 | 重要 |
| 报警内容 | String | 200 | 是 | 报警详细描述 | 10kV母线电压越上限 |
| 当前值 | Double | - | 是 | 当前数值 | 235.0 |
| 设定值 | Double | - | 是 | 设定阈值 | 230.0 |
| 报警时间 | DateTime | - | 是 | 报警发生时间 | 2025-05-31T10:30:00.000Z |
| 确认时间 | DateTime | - | 否 | 报警确认时间 | null |
| 确认人 | String | 20 | 否 | 确认操作员 | "" |
| 报警状态 | String | 10 | 是 | 已确认/未确认 | 未确认 |

#### 3.2.5 巡检任务模型

##### 3.2.5.1 主表结构

| 字段名称 | 数据类型 | 长度 | 必填 | 说明 | 示例值 |
|----------|----------|------|------|------|--------|
| 任务编号 | String | 30 | 是 | 任务唯一标识 | TASK_20250531_001 |
| 任务名称 | String | 50 | 是 | 任务中文名称 | 主变本体巡检 |
| 任务类型 | String | 20 | 是 | 定时/手动巡检 | 定时巡检 |
| 执行状态 | String | 20 | 是 | 执行中/已完成/已取消 | 执行中 |
| 开始时间 | DateTime | - | 是 | 任务开始时间 | 2025-05-31T10:00:00.000Z |
| 结束时间 | DateTime | - | 否 | 任务结束时间 | null |

##### 3.2.5.2 巡检点表结构

| 字段名称 | 数据类型 | 长度 | 必填 | 说明 | 示例值 |
|----------|----------|------|------|------|--------|
| 巡检点编号 | String | 20 | 是 | 巡检点标识 | POINT_001 |
| 巡检点名称 | String | 50 | 是 | 巡检点名称 | 主变油位 |
| 摄像头编号 | String | 20 | 是 | 关联摄像头 | CAM_001 |
| 预置位编号 | String | 20 | 是 | 预置位标识 | PRESET_001 |
| 停留时间 | Integer | - | 是 | 停留秒数 | 10 |
| 识别项目 | Array | - | 是 | 识别项目列表 | ["油位刻度"] |
| 执行状态 | String | 20 | 是 | 已完成/执行中/未开始 | 已完成 |
| 识别结果 | String | 200 | 否 | 识别结果描述 | 油位正常 |
| 抓图路径 | String | 200 | 否 | 图片存储路径 | /images/20250531/point_001.jpg |

#### 3.2.6 视频设备模型

##### ******* 主表结构

| 字段名称 | 数据类型 | 长度 | 必填 | 说明 | 示例值 |
|----------|----------|------|------|------|--------|
| 设备编号 | String | 20 | 是 | 设备唯一标识 | CAM_001 |
| 设备名称 | String | 50 | 是 | 设备中文名称 | 主变摄像头 |
| 设备类型 | String | 20 | 是 | 固定/球型摄像头 | 固定摄像头 |
| IP地址 | String | 15 | 是 | 设备IP地址 | ************0 |
| 端口号 | Integer | - | 是 | 视频端口 | 554 |
| 用户名 | String | 20 | 是 | 登录用户名 | admin |
| 在线状态 | String | 10 | 是 | 在线/离线 | 在线 |
| 视频状态 | String | 10 | 是 | 正常/异常 | 正常 |
| 云台功能 | Boolean | - | 是 | 是否支持云台 | true |

##### ******* 预置位表结构

| 字段名称 | 数据类型 | 长度 | 必填 | 说明 | 示例值 |
|----------|----------|------|------|------|--------|
| 预置位编号 | String | 20 | 是 | 预置位标识 | PRESET_001 |
| 预置位名称 | String | 50 | 是 | 预置位名称 | 主变全景 |
| 方位角 | Integer | - | 是 | 水平角度(0-360) | 180 |
| 俯仰角 | Integer | - | 是 | 垂直角度(-90到90) | 0 |
| 变倍 | Integer | - | 是 | 变倍倍数 | 1 |

### 3.3 配置数据模型

#### 3.3.1 测点配置模型

| 字段名称 | 数据类型 | 长度 | 必填 | 说明 | 示例值 |
|----------|----------|------|------|------|--------|
| 测点编号 | String | 20 | 是 | 测点唯一标识 | AI_001_001 |
| 测点名称 | String | 50 | 是 | 测点中文名称 | 10kV母线电压 |
| 测点类型 | String | 10 | 是 | 模拟量/开关量 | 模拟量 |
| 设备编号 | String | 20 | 是 | 所属设备编号 | RTU_001 |
| 通信地址 | Integer | - | 是 | Modbus地址 | 1001 |
| 数据类型 | String | 20 | 是 | 浮点型/整型/布尔型 | 浮点型 |
| 单位 | String | 10 | 否 | 工程单位 | kV |
| 系数 | Double | - | 否 | 变换系数 | 1.0 |
| 偏移量 | Double | - | 否 | 偏移量 | 0.0 |
| 上限值 | Double | - | 否 | 上限阈值 | 230.0 |
| 下限值 | Double | - | 否 | 下限阈值 | 200.0 |
| 越限报警 | Boolean | - | 是 | 是否启用越限报警 | true |
| 报警等级 | String | 10 | 否 | 重要/一般/提示 | 重要 |
| 存储周期 | Integer | - | 是 | 存储周期(秒) | 60 |
| 上送主站 | Boolean | - | 是 | 是否上送主站 | true |
| IEC104地址 | Integer | - | 否 | IEC104通信地址 | 1001 |

#### 3.3.2 设备配置模型

| 字段名称 | 数据类型 | 长度 | 必填 | 说明 | 示例值 |
|----------|----------|------|------|------|--------|
| 设备编号 | String | 20 | 是 | 设备唯一标识 | RTU_001 |
| 设备名称 | String | 50 | 是 | 设备中文名称 | 主变保护装置 |
| 设备类型 | String | 20 | 是 | 设备分类 | 保护装置 |
| 通信协议 | String | 20 | 是 | Modbus/OPC UA等 | Modbus |
| IP地址 | String | 15 | 是 | 设备IP地址 | ************ |
| 端口号 | Integer | - | 是 | 通信端口 | 502 |
| 从站地址 | Integer | - | 否 | Modbus从站地址 | 1 |
| 轮询周期 | Integer | - | 是 | 轮询周期(毫秒) | 1000 |
| 超时时间 | Integer | - | 是 | 超时时间(毫秒) | 3000 |
| 重试次数 | Integer | - | 是 | 通信重试次数 | 3 |
| 启用状态 | Boolean | - | 是 | 是否启用 | true |
| 所属分组 | String | 20 | 否 | 设备分组 | 主设备 |
| 安装位置 | String | 50 | 否 | 安装位置描述 | 主控室 |
| 投运日期 | Date | - | 否 | 设备投运日期 | 2025-01-01 |

## 4. 通信架构设计

### 4.1 通信协议架构

### 4.1 通信协议架构

#### 4.1.1 协议分层架构

系统通信协议采用分层架构设计，从上到下分为四个层次：

**应用层协议：**
- IEC104：用于主站通信的电力行业标准协议
- HTTPS：用于Web接口的安全超文本传输协议
- WebSocket：用于实时推送的双向通信协议
- FTP：用于文件传输的文件传输协议

**会话层协议：**
- HTTP：用于内部通信的超文本传输协议
- RTSP：用于视频流传输的实时流协议
- Redis：用于消息传递的内存数据库协议
- NTP：用于对时的网络时间协议

**传输层协议：**
- TCP：提供可靠传输的传输控制协议
- UDP：提供实时传输的用户数据报协议
- Modbus TCP：基于TCP的Modbus通信协议


#### 4.1.2 协议转换与数据流

系统实现多种协议间的转换和数据流转：

**外部接口协议转换：**
- 辅控主站通过IEC104协议(如C_IC_NA_1总召唤命令)与数据服务通信
- 数据服务将IEC104数据转换后通过HTTP REST API发送给后台服务
- 后台服务进行配置管理和数据缓存，数据存储在Redis中
- 实现遥测点和遥信点的状态监控和配置同步

**内部服务通信流：**
- 巡检服务通过HTTP GET请求从后台服务获取配置信息
- 后台服务通过配置API以JSON格式返回任务配置
- 巡检服务执行任务后将巡检结果和图片上传到后台服务
- 系统通过Redis消息队列进行事件通知和状态监控

**设备层协议适配：**
- 通信管理机通过Modbus协议进行数据采集，经协议解析后数据标准化
- 摄像头通过RTSP协议提供视频流，经H.264编码后进行存储分发
- 传感器通过485总线采集模拟量数据，经数值转换后实现实时监控

### 4.2 IEC104通信架构设计

#### 4.2.1 IEC104服务端架构

IEC104通信服务采用分层架构设计，从上到下分为四个功能层：

**应用层：**
- 遥信处理：处理M_SP_NA类型的单点信息和开关量状态
- 遥测处理：处理M_ME_NC类型的模拟量测量值
- 遥控处理：处理C_SC_NA类型的单命令和控制指令
- 文件传输：处理F_FR_NA类型的文件传输功能
- 对时：处理C_CS类型的时钟同步命令

**通信层：**
- 连接管理：负责IEC104连接的启动和停止
- 帧格式化：处理APDU应用协议数据单元的格式化
- 序号控制：管理发送和接收序号的控制
- 确认机制：处理ACK确认和NACK否认应答
- 心跳：执行测试帧的发送和接收

**传输层：**
- TCP连接：监听和维护TCP网络连接
- 端口管理：管理2404标准端口的网络通信
- 并发控制：支持多个客户端的并发连接
- 异常处理：处理网络超时和连接异常
- 重连：自动重连机制保证通信可靠性

**数据层：**
- 测点映射：建立IEC104地址与内部测点的映射关系
- 数据转换：进行不同数据类型之间的转换
- 质量标识：处理IV无效和BL闭锁等质量标识
- 时标处理：处理CP56Time2a格式的时间标签
- 缓存：基于Redis的数据缓存和快速访问

#### 4.2.2 IEC104信息类型定义

##### 4.2.2.1 遥信类型定义

| 信息类型 | 类型ID | 说明 | 时标支持 |
|----------|--------|------|----------|
| M_SP_NA_1 | 1 | 单点信息 | 否 |
| M_SP_TA_1 | 2 | 带时标的单点信息 | 是 |
| M_DP_NA_1 | 3 | 双点信息 | 否 |
| M_DP_TA_1 | 4 | 带时标的双点信息 | 是 |

##### 4.2.2.2 遥测类型定义

| 信息类型 | 类型ID | 说明 | 数据格式 |
|----------|--------|------|----------|
| M_ME_NA_1 | 9 | 测量值，标准化值 | 16位整型 |
| M_ME_NB_1 | 11 | 测量值，标度化值 | 16位标度值 |
| M_ME_NC_1 | 13 | 测量值，短浮点数 | 32位浮点型 |
| M_ME_TD_1 | 34 | 带时标的测量值，标准化值 | 16位整型+时标 |

##### 4.2.2.3 遥控类型定义

| 信息类型 | 类型ID | 说明 | 控制方式 |
|----------|--------|------|----------|
| C_SC_NA_1 | 45 | 单命令 | 开/关控制 |
| C_DC_NA_1 | 46 | 双命令 | 三态控制 |
| C_RC_NA_1 | 47 | 步调节命令 | 步进调节 |
| C_SE_NC_1 | 50 | 设定命令，短浮点数 | 数值设定 |

##### 4.2.2.4 系统类型定义

| 信息类型 | 类型ID | 说明 | 功能描述 |
|----------|--------|------|----------|
| C_IC_NA_1 | 100 | 总召唤命令 | 召唤全部数据 |
| C_CI_NA_1 | 101 | 计数器召唤命令 | 召唤计数器值 |
| C_RD_NA_1 | 102 | 读命令 | 读取指定数据 |
| C_CS_NA_1 | 103 | 时钟同步命令 | 系统时钟同步 |

### 4.3 视频通信架构

#### 4.3.1 视频流传输架构

视频流分发架构采用分层设计，确保视频数据的高效传输和分发：

**客户端层：**
- Qt客户端：支持直连和转发两种模式接收视频流
- 主站客户端：通过RTMP协议接收远程视频流

**采集层：**
- 摄像头(IP Camera)：网络摄像头直接提供数字视频流
- NVR设备：支持海康威视、大华股份等主流厂商设备

#### 4.3.2 视频控制协议

系统支持多种云台控制协议，确保与不同厂商设备的兼容性：

**云台控制协议支持：**
- PELCO-D：标准云台控制协议，广泛应用于监控系统
- PELCO-P：扩展云台控制协议，提供更多控制功能
- VISCA：索尼VISCA协议，适用于专业摄像设备
- 海康SDK：海康威视私有协议，优化的设备控制
- 大华SDK：大华股份私有协议，专用设备控制接口

**控制命令类型：**
- 方向控制：支持上下左右单方向控制和复合方向控制
- 变倍控制：支持放大、缩小和自动聚焦功能
- 预置位：支持预置位的设置、调用和删除操作
- 巡航路径：支持巡航路径的添加、删除、启动和停止
- 辅助功能：支持灯光控制、雨刷控制和加热器控制

### 4.4 设备通信接口

#### 4.4.1 现场设备通信协议

系统支持多种现场设备的通信协议，确保与不同类型设备的兼容性：

| 设备类型 | 通信协议 | 接口类型 | 数据格式 | 通信周期 |
|----------|----------|----------|----------|----------|
| 保护装置 | IEC61850 | 以太网 | GOOSE/MMS | 100ms |
| 测控装置 | Modbus TCP | 以太网 | 寄存器 | 1s |
| 智能仪表 | Modbus RTU | RS485 | 寄存器 | 5s |
| 环境传感器 | 自定义 | RS485 | ASCII | 10s |
| 摄像头 | ONVIF | 以太网 | SOAP/XML | 实时 |
| NVR设备 | SDK/RTSP | 以太网 | 私有/标准 | 实时 |
| 门禁控制器 | TCP/485 | 混合 | 私有协议 | 1s |
| 消防设备 | CAN总线 | CAN | 消防协议 | 5s |
| 空调设备 | BACnet | 以太网 | BACnet/IP | 30s |
| UPS设备 | SNMP | 以太网 | MIB | 60s |

### 4.6 通信性能规范

#### 4.6.1 通信性能指标（符合Q/CR要求）

系统通信性能符合Q/CR 1029-2024标准要求：

| 指标类型 | 性能要求 | 实现方式 | 验证方法 |
|----------|----------|----------|----------|
| 数据采集周期 | ≤ 5秒 | 多线程采集 | 性能测试 |
| 报警响应时间 | ≤ 1秒 | 事件驱动 | 响应时间测试 |
| 视频切换时间 | ≤ 1秒 | 流媒体技术 | 操作响应测试 |
| 控制响应时间 | ≤ 1秒 | 实时通信 | 控制延时测试 |
| 系统时钟精度 | ≤ 10毫秒 | NTP同步 | 时钟精度测试 |
| SOE分辨率 | ≤ 2毫秒 | 硬件时标 | 事件记录测试 |
| 遥测合格率 | ≥ 99.99% | 质量标识 | 数据质量统计 |
| 遥信响应率 | 100% | 变位检测 | 状态变化测试 |
| 遥控正确率 | 100% | 确认机制 | 控制命令测试 |
| 网络丢包率 | ≤ 0.01% | 冗余链路 | 网络质量监控 |

#### 5.4.2 权限控制策略

系统采用多层次权限控制策略，确保数据和功能的安全访问：

**权限模型设计：**

1. 基于角色的访问控制(RBAC)：
   - 用户与角色关联：用户被分配到一个或多个角色
   - 角色与权限关联：角色包含一组特定的权限
   - 权限与资源关联：权限控制对特定资源的访问
   
   关系模型包含：
   - 用户属性：用户基本信息、状态和配置
   - 角色属性：角色名称、描述和权限范围
   - 权限属性：权限名称、类型和作用域

2. 权限分类体系：
   - 功能权限：控制菜单访问和功能操作权限
   - 数据权限：控制数据查看、编辑和删除权限
   - 时间权限：访问时间段限制
   - 地点权限：IP地址/地理位置限制

3. 权限继承机制
   - 角色权限继承
   - 组织权限继承
   - 用户自定义权限

4. 动态权限控制
      - 基于属性的权限控制(ABAC)：支持上下文感知权限和实时权限计算

#### 5.4.3 数据安全保护策略

系统采用多层次数据安全保护策略：

**数据加密策略：**
- 传输加密：采用TLS 1.3/SSL协议保护数据传输安全
- 存储加密：使用AES-256算法对存储数据进行加密
- 字段加密：对敏感字段进行专门加密保护
- 密钥管理：实施密钥轮换机制确保密钥安全

## 6. 部署架构设计

### 6.1 部署模式

#### 6.1.1 标准部署架构

系统采用三层部署架构，确保性能和可扩展性：

**客户端服务器：**
- Qt客户端：提供桌面端完整功能界面
- Web客户端：提供基于浏览器的访问接口
- 运维客户端：提供系统运维管理功能

**应用服务器：**
- 后台服务：核心业务逻辑和API服务
- 数据服务：数据采集和IEC104协议服务
- 巡检服务：自动巡检任务执行服务
- 识别服务：AI图像识别处理服务

**数据库服务器：**
- MySQL：配置数据和关系型数据存储
- MongoDB：历史数据和文档型数据存储
- Redis：实时数据缓存和消息队列
- 文件存储：视频文件和图片文件存储




## 7. 性能与可扩展性设计

### 7.1 性能设计指标（符合Q/CR标准要求）

#### 7.1.1 系统可用性指标

**系统可用性指标要求：**

| 指标项目 | 标准要求 | 设计目标 | 实现措施 |
|----------|----------|----------|----------|
| 系统可用率 | ≥99.99% | ≥99.99% | 冗余架构设计 |
| 平均无故障时间(MTBF) | ≥30000h | ≥35000h | 高可靠组件 |
| 硬件MTBF | ≥50000h | ≥60000h | 工业级硬件 |
| 故障恢复时间(MTTR) | ≤30min | ≤15min | 快速诊断恢复 |
| 数据恢复时间(RTO) | ≤30min | ≤15min | 热备份机制 |
| 数据恢复点(RPO) | ≤15min | ≤5min | 实时备份 |

#### 7.1.2 实时性指标要求

**实时性指标要求：**

| 指标项目 | 标准要求 | 设计目标 | 技术保障措施 |
|----------|----------|----------|--------------|
| 视频控制切换响应时间 | ≤1s | ≤500ms | 专用视频通道 |
| 控制命令响应时间 | ≤1s | ≤500ms | 实时通信协议 |
| 画面显示延迟时间 | ≤1s | ≤500ms | 优化渲染算法 |
| 事件报警响应时间 | ≤1s | ≤300ms | 事件驱动机制 |
| 报警信息显示时间 | ≤1s | ≤500ms | 消息队列机制 |
| 系统时钟精度 | ≤10ms | ≤5ms | NTP+硬件对时 |
| SOE分辨率 | ≤2ms | ≤1ms | 硬件时戳机制 |
| 数据采集周期 | ≤5s | ≤3s | 多线程并发采集 |
| 历史数据查询响应 | ≤3s | ≤2s | 索引优化+缓存 |
| 报表生成时间 | ≤30s | ≤20s | 并行计算+缓存 |

#### 7.1.3 数据质量指标

**数据质量指标：**

| 指标项目 | 标准要求 | 设计目标 | 保障措施 |
|----------|----------|----------|----------|
| 遥测综合误差 | ≤0.5% | ≤0.3% | 高精度传感器+校准 |
| 遥测合格率 | ≥99.99% | ≥99.99% | 质量标识+校验 |
| 遥信响应率 | 100% | 100% | 变位检测+确认 |
| 遥控正确率 | 100% | 100% | 双确认+反校 |
| 数据完整性 | ≥99.9% | ≥99.95% | 校验码+冗余存储 |
| 数据一致性 | ≥99.9% | ≥99.95% | 事务管理+同步 |
| 时间同步精度 | ≤10ms | ≤5ms | NTP+GPS对时 |
| 网络丢包率 | ≤0.01% | ≤0.005% | 冗余链路+QoS |

## 8. 运维监控架构

### 8.1 监控体系架构

#### 8.1.1 全链路监控

**监控体系架构分为三个层次：**

**监控告警平台层：**
- 告警中心：集中处理和管理各类系统告警
- 监控大屏：实时展示系统运行状态和关键指标
- 趋势分析：历史数据分析和趋势预测
- 报表系统：自动生成各类运维报表

**监控数据层：**
- 应用监控：监控各业务应用的运行状态和性能指标
- 基础监控：监控服务器、网络、存储等基础设施
- 业务监控：监控业务流程和关键业务指标
- 日志监控：集中收集和分析系统日志

**数据采集层：**
- Prometheus：指标数据收集和存储
- Grafana：数据可视化和仪表板展示
- ELK Stack：日志收集、处理和分析
- Jaeger：分布式链路追踪



## 9. 技术选型说明

### 9.1 开发技术栈

#### 9.1.1 后端技术栈
- **框架**：ASP.NET Core Web API
- **开发语言**：C# 8.0
- **ORM框架**：Entity Framework Core
- **缓存**：Redis
- **消息队列**：Redis Pub/Sub
- **日志**：Serilog
- **测试**：xUnit

#### 9.1.2 前端技术栈
- **桌面客户端**：Qt 5.13(C++)
- **Web客户端**：Vue.js 3.0
- **实时通信**：WebSocket
- **图表组件**：ECharts

#### 9.1.3 数据库技术栈
- **关系数据库**：MySQL 8.0
- **文档数据库**：MongoDB 4.2
- **内存数据库**：Redis 4.0

### 9.2 第三方组件

#### 9.2.1 通信组件
- **IEC104协议栈**：lib60870
- **Modbus协议栈**：NModbus
- **视频组件**：HikSDK,DahuaSDK

#### 9.2.2 AI识别组件
- **图像识别**：OpenCV
- **深度学习**：PyTorch

## 10. 客户端功能模块详细设计

变电所辅助监控系统客户端采用Qt C++技术开发，为运维人员提供直观、高效的监控操作界面。客户端严格遵循Q/CR 1029-2024《牵引变电所辅助监控系统技术规范》中的软件界面要求，实现对变电所内各类辅助设备的综合监控与管理。

### 10.1 客户端总体架构

#### 10.1.1 技术架构设计

客户端采用分层架构设计，确保界面响应速度和系统稳定性：

**界面展示层**：
- Qt5.13界面框架：提供现代化的用户界面控件
- 自定义组件库：标准化的监控界面组件
- 多媒体播放：基于Qt多媒体框架的视频播放
- 图表展示：集成ECharts的数据可视化组件

**业务逻辑层**：
- 数据管理：实时数据缓存和处理
- 设备控制：各类设备的操作控制逻辑
- 报警处理：报警信息的接收和处理流程
- 用户管理：权限验证和用户行为管理

**通信接口层**：
- HTTP客户端：与后台服务进行RESTful API调用
- WebSocket客户端：接收实时数据推送
- Redis订阅：订阅消息队列获取实时信息
- 视频流接口：直接连接NVR和摄像头设备

**数据存储层**：
- 本地缓存：SQLite数据库存储临时数据
- 配置存储：本地配置文件管理
- 多媒体缓存：视频截图和录像文件管理

#### 10.1.2 界面布局规范

根据数据规范要求，客户端界面采用标准三区域布局：

**区域1 - 通用信息功能区**：
- 系统标题栏：显示企业logo、系统名称、版本号
- 用户信息区：当前登录用户、登录时间、权限等级
- 系统状态区：网络连接状态、设备在线状态
- 通用操作区：注销、设置、帮助等功能入口

**区域2 - 功能导航区**：
- 主要功能模块：首页、视频监控、智能巡检、综合监控、红外测温、导轨式巡检
- 快捷功能入口：报警确认、设备控制、数据查询
- 模块切换导航：支持自定义功能模块顺序

**区域3 - 主要展示操作区**：
- 内容展示：各功能模块的主要操作界面
- 实时数据：设备状态、测量值、报警信息
- 交互操作：设备控制、参数设置、数据查询

### 10.2 用户认证与权限模块

#### 10.2.1 登录认证功能

**登录界面设计**：
- 企业标识展示：显示企业logo和系统标识
- 用户凭证输入：用户名和密码输入框，支持记住用户名
- 安全机制：密码强度要求，防止弱口令使用
- 验证码机制：多次登录失败后启用图形验证码
- 登录状态保持：支持自动登录功能（可配置关闭）

**身份验证流程**：
1. 用户输入凭证信息
2. 客户端向后台服务发送认证请求
3. 后台验证用户身份和权限
4. 返回JWT Token和用户权限信息
5. 客户端缓存Token并建立会话

**安全控制措施**：
- 密码加密传输：采用HTTPS协议和RSA加密
- 会话超时管理：无操作自动注销机制
- 登录失败锁定：连续失败3次锁定账户30分钟
- 操作日志记录：记录所有用户登录和操作行为

#### 10.2.2 权限控制体系

**角色权限模型**：
- 系统管理员：拥有全部功能权限，可管理用户和系统配置
- 运维值班员：具有监控查看和基本控制权限
- 巡检操作员：专门负责巡检任务的执行和管理
- 只读用户：仅具有监控信息查看权限

**功能权限控制**：
- 菜单级权限：控制功能模块的访问权限
- 操作级权限：控制具体操作按钮的可见性
- 数据级权限：控制数据查看范围和详细程度
- 时间权限：限制用户的登录时间段

**界面权限适配**：
- 动态菜单加载：根据用户权限动态显示功能菜单
- 按钮状态控制：无权限操作的按钮显示为禁用状态
- 数据脱敏显示：敏感数据根据权限等级显示不同详细程度

### 10.3 主页展示模块

#### 10.3.1 电子地图展示

**地图显示功能**：
- 变电所平面图：基于CAD图纸的高精度平面布局图
- 设备位置标注：在地图上精确标注各类设备位置
- 实时状态显示：设备运行状态通过颜色和图标直观展示
- 分层显示：支持按楼层、区域进行分层展示

**交互操作功能**：
- 地图缩放平移：支持鼠标滚轮缩放和拖拽平移
- 设备点击查看：点击设备图标弹出详细信息窗口
- 区域选择过滤：点击区域显示该区域内的设备列表
- 快速定位：通过设备名称搜索快速定位到地图位置

**状态信息展示**：
- 设备运行状态：正常（绿色）、报警（红色）、故障（黄色）、离线（灰色）
- 环境监测数据：在相应位置显示温湿度、水位等环境参数
- 报警信息提示：报警设备闪烁显示并显示报警类型

#### 10.3.2 三维场景展示

**三维模型功能**：
- 3D场景建模：基于实际建筑结构的三维场景模型
- 设备三维展示：主要设备的3D模型精确展示
- 视角切换：支持自由视角、预设视角切换
- 漫游功能：支持场景内的自由漫游和导航

**设备状态集成**：
- 实时数据叠加：在3D模型上叠加显示实时监测数据
- 状态颜色映射：设备模型颜色反映当前运行状态
- 报警动效：报警设备通过动画效果醒目提示

#### 10.3.3 系统运行状态总览

**运行参数展示**：
- 系统在线率：各子系统的在线设备统计
- 通信状态：与各设备的通信连接状态
- 数据更新状态：实时数据的更新频率和延迟
- 存储空间使用：系统存储空间使用情况

**报警信息汇总**：
- 实时报警统计：按严重程度分类统计当前报警数量
- 报警趋势图表：显示最近24小时的报警趋势
- 子系统报警分布：各子系统的报警数量分布饼图
- 报警处理状态：已确认、未确认、已处理报警统计

**快捷操作入口**：
- 紧急控制：紧急照明、消防系统等快捷控制按钮
- 报警确认：一键确认所有报警或批量确认
- 系统设置：常用系统参数的快速设置入口

### 10.4 视频监控模块

#### 10.4.1 实时视频监控

**多分屏显示功能**：
- 分屏模式：支持1、4、9、16分屏显示模式切换
- 画面布局：灵活的画面布局，支持自定义分屏组合
- 全屏显示：双击视频窗口可全屏播放
- 窗口拖拽：支持视频窗口间的拖拽交换

**摄像头控制功能**：
- 云台控制：上下左右方向控制，支持连续旋转和点动控制
- 变倍变焦：镜头的光学变倍和数字变焦控制
- 预置位调用：快速调用预设的摄像头位置和角度
- 巡航设置：设置摄像头的自动巡航路径和停留时间

**视频操作功能**：
- 实时录像：手动开始/停止录像，支持定时录像
- 图像抓拍：实时抓拍当前画面并保存
- 音频对讲：双向语音对讲功能（如设备支持）
- 画面标注：在视频画面上添加文字标注和测量标尺

**视频质量控制**：
- 码流切换：主码流和子码流之间的灵活切换
- 画质调节：亮度、对比度、饱和度的实时调节
- 网络适应：根据网络状况自动调整视频质量
- 缓冲控制：视频播放缓冲时间的优化

#### 10.4.2 视频回放模块

**历史视频查询**：
- 时间范围选择：精确到秒的时间范围选择
- 多摄像头选择：同时选择多个摄像头进行回放
- 事件关联查询：根据报警事件时间快速定位视频
- 标签搜索：通过视频标签快速查找相关录像

**回放控制功能**：
- 播放控制：播放、暂停、停止、快进、快退
- 速度调节：支持1/8倍到16倍速的播放速度调节
- 进度控制：时间轴拖拽快速定位到指定时间点
- 同步回放：多路视频的时间同步回放

**视频下载导出**：
- 片段下载：选择时间段下载视频片段
- 批量导出：批量导出多个摄像头的录像文件
- 格式转换：支持MP4、AVI等主流视频格式导出
- 水印添加：导出视频自动添加时间和位置水印

#### 10.4.3 摄像头配置管理

根据数据规范要求，系统支持以下摄像头类型的配置管理：

**设备类型支持**：
- 固定摄像头：枪机、半球机等固定位置监控设备
- 云台摄像头：云台枪机、球机等可控制型监控设备
- 热成像设备：热成像枪机、热成像云台、热成像球机
- 双光谱设备：可见光（A通道）和热成像（B通道）双通道设备
- 导轨式巡检：导轨式巡检摄像机的位置和预置位控制

**预置位管理**：
- 预置位配置：按照数据规范要求配置预置位信息
- 拍摄焦点设置：根据设备类型设置对应的拍摄焦点
- 命名规范管理：严格按照"运行编号-拍摄焦点名称"格式命名
- 区域关联：将预置位与变电所安装区域进行关联

**视频显示规范**：按照数据规范E.2.2要求，在视频画面上叠加显示：
- 当前时间：画面左上角，格式"YYYY-MM-DD HH:mm:ss"
- 摄像机名称：画面右上角，与设备配置表中名称一致
- 预置位名称：画面右下角，显示当前预置位名称
- 所亭名称：画面左下角，显示"XX变电所/AT所/分区所/配电所"

### 10.5 智能巡检模块

#### 10.5.1 自动巡检任务管理

**巡检任务配置**：
- 任务计划设置：支持按时间、周期、事件触发的巡检计划
- 巡检路径规划：根据设备分布规划最优巡检路径
- 检查项目配置：为每个巡检点配置具体的检查项目
- 异常判断标准：设置各类异常的判断阈值和规则

**巡检执行流程**：
1. 任务调度启动：根据预设计划自动启动巡检任务
2. 设备定位控制：导轨式巡检摄像机移动到指定位置
3. 预置位调用：调用预设的摄像头角度和焦距
4. 图像采集：采集高清图像用于后续分析
5. AI识别分析：调用识别服务进行图像智能分析
6. 结果判断：根据识别结果判断设备状态
7. 异常报告：发现异常时生成报警信息
8. 任务完成：返回巡检结果并记录日志

**智能识别功能**：
- 设备状态识别：开关位置、指示灯状态、仪表读数
- 设备缺陷检测：渗油、破损、变形、腐蚀等异常
- 环境异常检测：烟雾、火焰、积水、异物入侵
- 温度异常分析：基于红外热成像的温度监测

#### 10.5.2 手动巡检功能

**手动巡检操作**：
- 实时巡检：运维人员可随时启动手动巡检
- 路径自定义：临时创建巡检路径和检查点
- 现场记录：支持实时添加文字备注和语音记录
- 图像标注：在采集的图像上标注异常区域

**巡检模板管理**：
- 模板创建：基于常用巡检流程创建标准模板
- 模板编辑：灵活修改模板的检查项目和参数
- 模板应用：快速应用模板执行巡检任务
- 模板共享：在不同时段和人员间共享巡检模板

#### 10.5.3 巡检记录与分析

**历史记录查询**：
- 多条件查询：按时间、设备、操作员、结果等条件查询
- 巡检轨迹回放：显示历史巡检的完整路径和时间
- 结果对比分析：对同一设备不同时间的巡检结果进行对比
- 异常趋势分析：统计分析设备异常发生的规律和趋势

**报告生成功能**：
- 日报生成：每日巡检情况的汇总报告
- 周期报告：周报、月报、年报的自动生成
- 异常报告：专门针对发现异常的详细分析报告
- 自定义报告：根据用户需求生成定制化报告

### 10.6 综合监控模块

综合监控模块集成了环境监测、安全防范、火灾报警、动力照明控制等多个子系统，实现一体化监控管理。

#### 10.6.1 环境监测子系统

**监测参数管理**：
- 温湿度监测：各区域的实时温度和湿度值
- SF6气体监测：SF6浓度的实时监测和泄漏报警
- 水位监测：水浸传感器的水位状态监测
- 空气质量：PM2.5、CO2等空气质量参数
- 微气象信息：风速、风向、气压、雨量等

**数据展示方式**：
- 实时数值显示：大字体数值显示当前测量值
- 历史趋势曲线：显示最近24小时/7天/30天的数据趋势
- 阈值告警设置：上下限阈值的设置和超限报警
- 多参数对比：不同区域同类参数的对比显示

**报警处理机制**：
- 实时报警：参数超限时立即触发声光报警
- 报警分级：根据超限程度分为提醒、警告、严重三个等级
- 联动控制：触发相关设备的自动控制（如启动通风）
- 报警记录：详细记录报警时间、参数值、处理过程

#### 10.6.2 安全防范子系统

**入侵检测功能**：
- 周界防护：红外对射、激光对射、电子围栏的状态监控
- 区域防护：红外双鉴、红外三鉴探头的区域入侵检测
- 玻璃破碎检测：重要区域玻璃破碎传感器的状态监控
- 视频移动检测：基于视频分析的移动目标检测

**门禁管理功能**：
- 门禁状态监控：各个门禁点的开关状态实时监控
- 刷卡记录查询：人员刷卡进出的详细记录查询
- 权限管理：不同人员对不同区域的门禁权限管理
- 远程开门：紧急情况下的远程开门控制

**安防联动功能**：
- 视频联动：报警触发时自动切换到相关摄像头
- 照明联动：入侵检测时自动开启相关区域照明
- 声光报警：触发报警时启动现场声光报警设备
- 通知推送：重要报警信息推送到相关管理人员

#### 10.6.3 火灾报警子系统

**火灾检测功能**：
- 烟感检测：烟雾探测器的实时状态监控
- 温感检测：温度探测器的温度监控和火灾预警
- 手动报警：手动报警按钮的状态监控
- 视频火焰检测：基于视频分析的火焰和烟雾检测

**消防联动控制**：
- 气体灭火：自动触发气体灭火装置
- 声光报警：启动现场声光报警器
- 排烟风机：自动启动排烟系统
- 应急照明：启动应急照明和疏散指示

**消防管理功能**：
- 消防设备状态：灭火器、消火栓等消防设施的状态管理
- 逃生路线指示：动态显示最佳疏散路线
- 消防演练：支持消防演练模式的配置和执行
- 火灾记录：详细记录火灾事件的发生、发展和处理过程

#### 10.6.4 动力照明控制子系统

**照明控制功能**：
- 分区控制：按区域对照明系统进行分组控制
- 定时控制：设置照明的自动开关时间
- 光感控制：根据环境光线自动调节照明亮度
- 应急照明：紧急情况下的应急照明控制

**动力设备控制**：
- 风机控制：通风风机的启停控制和转速调节
- 水泵控制：排水泵、消防泵的启停控制
- 空调控制：空调系统的温度设置和运行控制
- UPS监控：不间断电源的运行状态监控

**能耗管理功能**：
- 实时功率监测：各设备的实时功率消耗监测
- 能耗统计分析：日、月、年的能耗统计和分析
- 节能控制策略：根据实际需求优化设备运行策略
- 成本核算：根据能耗数据进行运行成本核算

### 10.7 红外测温模块

#### 10.7.1 实时测温监控

**双光谱视频显示**：
- 可见光画面：标准的彩色视频画面，用于设备识别
- 热成像画面：伪彩色温度分布图，直观显示温度差异
- 画中画模式：在可见光画面中叠加热成像画面
- 画面同步：两个画面的视角和缩放比例同步控制

**测温功能操作**：
- 测温点设置：在画面上设置固定测温点
- 测温区域：设置矩形、圆形、多边形测温区域
- 测温线：设置线性测温，显示沿线的温度分布
- 最高/最低温点：自动标识画面中的最高温和最低温点

**温度显示功能**：
- 实时温度值：在测温点位置显示具体温度数值
- 温度色彩映射：通过颜色直观显示温度高低
- 温升计算：计算相对于环境温度的温升值
- 趋势曲线：显示测温点的温度变化趋势

**环境温度补偿**：
- 环境温度获取：从环境监测系统获取当前环境温度
- 自动温度补偿：根据环境温度对测温结果进行修正
- 反射温度补偿：考虑被测物体的发射率进行补偿
- 距离补偿：根据测温距离进行大气衰减补偿

#### 10.7.2 周期测温巡检

**测温任务调度**：
- 定时测温：按照预设时间间隔自动执行测温
- 测温路径：规划测温巡检的路径和停留时间
- 批量测温：对多个设备进行批量自动测温
- 任务优先级：根据设备重要程度设置测温优先级

**阈值报警管理**：
- 温度阈值设置：为不同设备设置不同的温度报警阈值
- 温升阈值：设置相对温升的报警阈值
- 分级报警：设置注意、警告、严重三级温度报警
- 报警抑制：避免温度波动引起的频繁报警

**数据存储上送**：
- 历史数据存储：将测温数据存储到历史数据库
- IEC104上送：将温度数据按104协议格式上送主站
- 报警信息上送：温度报警信息实时上送主站
- 数据压缩：历史数据的压缩存储和管理

**测温报告功能**：
- 测温日报：每日测温结果的汇总报告
- 异常温度报告：温度异常设备的专项分析报告
- 温度趋势分析：设备温度的长期变化趋势分析
- 对比分析：同类设备温度的横向对比分析

### 10.8 报警管理模块

#### 10.8.1 实时报警处理

**报警信息接收**：
- 多源报警集成：集成各子系统的报警信息
- 实时报警显示：报警信息的实时弹窗显示
- 报警声音提示：不同等级报警的声音区分提示
- 报警闪烁提示：报警区域的LED指示灯闪烁

**报警分级管理**：
- 一级报警（严重）：火灾、SF6泄漏等重大安全事件
- 二级报警（警告）：设备故障、环境异常等
- 三级报警（提醒）：参数超限、设备维护提醒等
- 报警优先级：根据报警等级确定处理优先顺序

**报警确认处理**：
- 单条确认：对单个报警信息进行确认
- 批量确认：对多条同类报警进行批量确认
- 确认权限：不同等级用户的报警确认权限
- 处理记录：记录报警确认人员、时间、处理措施

**报警联动功能**：
- 视频联动：报警时自动切换到相关摄像头画面
- 照明联动：触发报警区域的照明设备
- 设备联动：根据报警类型自动控制相关设备
- 通知联动：重要报警自动发送短信或邮件通知

#### 10.8.2 历史报警查询

**查询条件设置**：
- 时间范围：设置查询的起始和结束时间
- 报警等级：按照报警严重程度进行筛选
- 设备类型：按照设备类型或所属子系统筛选
- 报警状态：已确认、未确认、已处理等状态筛选

**报警统计分析**：
- 报警数量统计：按时间段统计各类报警的数量
- 报警频率分析：分析报警发生的频率和规律
- 设备故障率：统计各设备的故障频率和可靠性
- 报警处理效率：统计报警确认和处理的时间效率

**报警趋势分析**：
- 时间趋势图：显示报警数量随时间的变化趋势
- 分类统计图：不同类型报警的占比分析
- 区域分布图：报警在不同区域的分布情况
- 周期性分析：发现报警发生的周期性规律

### 10.9 统计分析模块

#### 10.9.1 数据统计功能

**实时数据统计**：
- 测量值统计：实时显示各类测量参数的当前值
- 状态量统计：统计各类开关状态和设备运行状态
- 在线率统计：计算各子系统和设备的在线率
- 通信质量统计：统计数据通信的成功率和延迟

**历史数据分析**：
- 数据趋势分析：显示测量参数的历史变化趋势
- 峰值谷值统计：统计数据的最大值、最小值和平均值
- 数据分布分析：分析数据的正态分布和异常值
- 相关性分析：分析不同参数之间的关联关系

**多参数对比**：
- 同类参数对比：不同设备同类参数的横向对比
- 不同时间对比：同一参数在不同时间段的对比
- 环境影响分析：分析环境因素对设备参数的影响
- 负载特性分析：分析设备在不同负载下的运行特性

#### 10.9.2 报表管理功能

**标准报表生成**：
- 日报表：每日运行数据和事件的汇总报表
- 周报表：一周内的数据统计和异常分析报表
- 月报表：月度运行质量和设备状态报表
- 年报表：年度运行总结和设备维护计划报表

**自定义报表**：
- 报表模板设计：用户可自定义报表的格式和内容
- 数据源配置：灵活配置报表的数据来源和计算方式
- 图表类型选择：支持表格、柱状图、线图、饼图等多种展示方式
- 报表调度：设置报表的自动生成时间和频率

**报表导出分发**：
- 多格式导出：支持PDF、Excel、Word等格式导出
- 邮件自动发送：定期自动发送报表到指定邮箱
- 打印功能：直接打印报表或预览打印效果
- 报表存档：自动归档历史报表文件

### 10.10 系统管理模块

#### 10.10.1 用户权限管理

**用户账户管理**：
- 用户信息维护：用户基本信息的增删改查
- 密码策略管理：密码复杂度、有效期、历史密码等策略
- 账户状态管理：账户的启用、禁用、锁定状态管理
- 登录限制：IP地址限制、时间段限制等安全策略

**角色权限配置**：
- 角色定义：定义不同的用户角色和权限范围
- 权限分配：为角色分配具体的功能和数据权限
- 权限继承：支持角色之间的权限继承关系
- 权限审计：记录权限变更的历史和操作人员

#### 10.10.2 系统配置管理

**基础参数配置**：
- 网络连接配置：服务器地址、端口、超时时间等
- 数据库连接：配置各数据库的连接参数
- 设备通信参数：各类设备的通信协议和参数
- 界面个性化：主题、字体、颜色等界面个性化设置

**报警阈值设置**：
- 测量阈值：各类测量参数的上下限阈值设置
- 温度阈值：红外测温的各级报警阈值
- 通信超时：设备通信超时的判断时间
- 数据质量：数据有效性判断的质量标准

**系统维护功能**：
- 数据备份：系统配置和历史数据的定期备份
- 日志清理：系统日志的自动清理和归档
- 软件升级：客户端软件的在线升级功能
- 系统诊断：系统运行状态的自动诊断和健康检查

#### 10.10.3 操作日志管理

**日志记录功能**：
- 用户操作日志：记录所有用户的登录和操作行为
- 系统运行日志：记录系统启动、停止、异常等事件
- 通信日志：记录与各设备的通信过程和结果
- 错误日志：记录系统运行过程中的错误和异常

**日志查询分析**：
- 多条件查询：按时间、用户、操作类型等条件查询
- 日志统计：统计用户操作频率和系统运行状况
- 异常分析：分析系统异常的原因和发生规律
- 安全审计：基于日志进行系统安全审计

### 10.11 智能识别模块

#### 10.11.1 图像识别功能

**设备状态识别**：
- 开关位置识别：自动识别断路器、隔离开关的分合闸状态
- 指示灯状态：识别各类指示灯的颜色和闪烁状态
- 仪表读数识别：自动读取指针式和数字式仪表的示数
- 设备标识识别：OCR识别设备铭牌和标识信息

**设备缺陷检测**：
- 渗油检测：识别变压器、开关等设备的渗油缺陷
- 破损检测：识别设备外壳破损、绝缘子破裂等缺陷
- 变形检测：识别设备的变形、倾斜等异常
- 腐蚀检测：识别金属部件的锈蚀和腐蚀情况

**环境异常识别**：
- 烟雾检测：基于视频分析的烟雾检测
- 火焰识别：实时识别画面中的火焰和高温目标
- 积水检测：识别设备区域的积水情况
- 异物检测：识别鸟巢、垃圾等异物入侵

#### 10.11.2 行为分析功能

**安全行为监控**：
- 区域入侵检测：检测非授权人员进入禁止区域
- 徘徊滞留检测：检测人员在敏感区域的异常徘徊
- 攀爬检测：检测人员攀爬围墙、设备等危险行为
- 物品遗留检测：检测可疑物品的遗留和放置

**人员行为分析**：
- 人数统计：统计特定区域内的人员数量
- 轨迹跟踪：跟踪人员在监控区域内的移动轨迹
- 聚集检测：检测人员异常聚集的情况
- 摔倒检测：检测人员摔倒等紧急情况

#### 10.11.3 识别结果管理

**识别数据处理**：
- 结果标准化：将识别结果转换为标准格式的数据
- 置信度评估：对识别结果的可信度进行评估
- 误识别过滤：通过多种算法减少误识别和漏识别
- 数据质量控制：确保识别数据的准确性和完整性

**识别结果应用**：
- 报警联动：识别到异常时自动触发报警
- 数据上送：将识别结果上送到主站系统
- 统计分析：对识别结果进行统计分析和趋势预测
- 报告生成：生成识别结果的分析报告

---

## 附录A：技术规范与标准

### A.1 引用标准规范

#### A.1.1 国家标准
- **GB/T 2887-2011**：计算机场地通用规范
- **GB 50174-2017**：数据中心设计规范
- **GB/T 20269-2006**：信息安全技术 信息系统安全管理要求
- **GB/T 22240-2020**：信息安全技术 网络安全等级保护定级指南
- **GB/T 25070-2019**：信息安全技术 网络安全等级保护安全设计技术要求

#### A.1.2 行业标准
- **Q/CR 796-2020**：牵引变电所辅助监控系统主站技术规范
- **Q/CR 1029-2024**：牵引变电所辅助监控系统技术规范
- **DL/T 634.5104-2002**：远动设备及系统 第5-104部分：传输协议
- **DL/T 476-2017**：电力系统实时数据通信应用层协议

#### A.1.3 国际标准
- **IEC 61850**：变电站通信网络和系统
- **IEC 60870-5-104**：远动设备及系统传输协议
- **IEC 62351**：电力系统管理和相关信息交换数据和通信安全
- **IEEE 802.1Q**：虚拟局域网标准

### A.2 系统接口规范

#### A.2.1 IEC104协议接口规范

**通信参数配置：**
- 端口号：2404 (标准端口)
- 连接超时：30秒
- 确认超时：15秒
- 测试周期：20秒
- 最大未确认数：8
- 最大未测试数：12
- 启动字符：0x68

**信息对象地址分配：**
- 遥信地址：1-9999
- 遥测地址：10000-19999
- 遥控地址：20000-29999

**时标格式：CP56Time2a**
- 毫秒：0-59999
- 分钟：0-59
- 小时：0-23
- 日期：1-31
- 月份：1-12
- 年份：0-99

**信息类型支持：**
- M_SP_NA_1 (1)：单点信息
- M_DP_NA_1 (3)：双点信息
- M_ME_NC_1 (13)：测量值，短浮点数
- M_SP_TB_1 (30)：带时标的单点信息
- C_SC_NA_1 (45)：单命令
- C_IC_NA_1 (100)：总召唤命令

#### A.2.2 数据交换接口规范

##### A.2.2.1 HTTP API接口规范

| 配置项 | 值 | 说明 |
|--------|----|----- |
| 基础URL | https://server:port/api/v1/ | API服务基础地址 |
| 认证方式 | Bearer Token + HTTPS | 认证和安全传输 |
| 数据格式 | JSON | 数据交换格式 |
| 字符编码 | UTF-8 | 字符编码标准 |

**响应格式规范：**

| 字段名称 | 数据类型 | 必填 | 说明 | 示例值 |
|----------|----------|------|------|--------|
| code | Integer | 是 | 响应状态码 | 200 |
| message | String | 是 | 响应描述信息 | 操作成功 |
| data | Object | 否 | 响应数据内容 | {...} |
| timestamp | Long | 是 | 响应时间戳 | 1627899600000 |

**状态码说明：**
- 200：操作成功
- 400：请求参数错误
- 401：认证失败
- 403：权限不足
- 404：资源不存在
- 500：服务器内部错误

##### A.2.2.2 WebSocket接口规范

| 配置项 | 值 | 说明 |
|--------|----|----- |
| 连接URL | wss://server:port/ws | WebSocket连接地址 |
| 心跳间隔 | 30秒 | 心跳保活间隔 |
| 重连策略 | 指数退避 | 断线重连策略 |

**消息格式规范：**

| 字段名称 | 数据类型 | 必填 | 说明 | 示例值 |
|----------|----------|------|------|--------|
| type | String | 是 | 消息类型 | heartbeat |
| data | Object | 否 | 消息内容 | {...} |
| timestamp | Long | 是 | 消息时间戳 | 1627899600000 |

##### A.2.2.3 数据库接口规范

**连接池配置：**

| 配置项 | 值 | 说明 |
|--------|----|----- |
| 初始连接数 | 10 | 连接池初始化连接数 |
| 最大连接数 | 100 | 连接池最大连接数 |
| 连接超时 | 30秒 | 获取连接超时时间 |
| 空闲超时 | 300秒 | 连接空闲回收时间 |

**事务管理：**

| 配置项 | 值 | 说明 |
|--------|----|----- |
| 隔离级别 | READ_COMMITTED | 事务隔离级别 |
| 超时时间 | 30秒 | 事务执行超时时间 |
| 重试次数 | 3 | 事务失败重试次数 |

### A.3 安全配置规范

#### A.3.1 网络安全配置

**防火墙策略配置：**

**入站规则：**
- 允许：TCP 4431 (HTTPS)
- 允许：TCP 2404 (IEC104) 
- 允许：TCP 8000 (NVR)
- 拒绝：其他所有端口

**IDS/IPS规则：**
- 检测：端口扫描
- 检测：暴力破解
- 检测：SQL注入
- 检测：XSS攻击
- 阻断：异常流量

#### A.3.2 应用安全配置

**安全配置规范：**

**认证配置：**
- 密码策略：
  - 最小长度：8位字符
  - 复杂度要求：大小写字母+数字+特殊字符
  - 有效期：90天
  - 历史密码：保留5个
- 会话配置：
  - 超时时间：30分钟
  - 最大并发：3个会话
  - 强制登出：支持

**加密配置：**
- 传输加密：TLS 1.3
- 存储加密：AES-256-GCM
- 密钥管理：PBKDF2+HMAC-SHA256
- 证书管理：X.509证书

**审计配置：**
- 日志级别：INFO
- 保留时间：3年
- 审计内容：登录+操作+配置变更
- 实时告警：异常登录+权限变更

### A.4 部署实施指南

#### A.4.1 硬件环境要求

**服务器配置规范：**

**应用服务器：**
- CPU：Intel 8核心 2.4GHz或以上
- 内存：32GB
- 存储：SSD 500GB + HDD 2TB
- 网卡：双千兆网卡
- 操作系统：Windows 10

#### A.4.2 软件环境要求

**基础软件环境：**

**Web服务器：**
- kestrel或Nginx 1.18
- .NET 6.0运行时环境
- SSL证书配置

数据库软件：
- MySQL 8.0.25
- Redis 4.0
- MongoDB 4.2

开发运行环境：
- Visual Studio 2022与VS Code
- Git版本控制
- Node.js 16.0+


#### A.5.2 性能监控指标

**关键性能指标(KPI)监控：**

**系统可用性指标：**
- 系统可用率：目标≥99.99%
- 服务响应时间：目标≤1秒
- 故障平均修复时间：目标≤30分钟
- 计划外停机时间：目标≤4小时/年

业务性能指标：
- 数据采集成功率：目标≥99.99%
- 报警响应时间：目标≤1秒
- 视频切换时间：目标≤1秒
- 并发用户支持：目标≥100用户

资源使用指标：
- CPU使用率：正常≤70%，告警≥85%
- 内存使用率：正常≤80%，告警≥90%
- 磁盘使用率：正常≤80%，告警≥90%
- 网络带宽使用率：正常≤70%，告警≥85%

