
目录
1 引言	1
1.1 编写目的	1
1.2 背景	1
1.3 定义	1
1.4 参考资料	1
2 业务总体描述	1
2.1 软件功能描述	1
3 模块一设计	1
3.1 时序图	1
3.2 功能描述	2
3.3 功能实现规则	2
3.4 算法	2
3.5 数据结构	2
3.6 用户界面	2
4 模块二设计	2
4.1 时序图	2
4.2 功能描述	3
4.3 功能实现规则	3
4.4 算法	3
4.5 数据结构	3
4.6 用户界面	3
5 非功能性设计	3
 
引言
编写目的
说明编写这份详细设计说明书的目的，指出预期的读者。
背景
说明：
a)	需开发的软件系统的名称；
b)	列出此项目的任务提出者、开发者、用户以及将运行该软件的计算环境。
定义
列出本文件中用到的专门术语的定义和外文首字母组词的原词组。
参考资料
列出有关的参考资料，如：
a)	本项目经核准的计划任务书或合同、上级机关的批文；
b)	属于本项目的其他已发表的文件；
c)	本文件中各处引用的文件、资料、包括所要用到的软件开发标准。
d)	列出这些文件资料的标题、文件编号、发表日期和出版单位，说明能够得到这些文件资料的来源。
1.	业务总体描述
1.1.	软件功能描述
描述被开发软件的功能，如有同等作用的文件（如已编写的《软件功能规格说明书》）则可直接在此引述该文件名及归档的部门即可。
1.2.	模块一设计
1.2.1.	时序图
简述各模块的功能。列明不同角色之间的使用，需要说明权限分配；比如不同用户可以使用特定的菜单功能；应体现各个模块之间的关联关系，时序图，有业务关系的功能模块可以增加数据控制流程图；
1.2.2.	功能描述
包括：
a)	说明本模块的输入/输出参数（命名标识）和调用方式
b)	说明与本模块相直接关联的数据结构（数据库、表文件）。

1.2.3.	功能实现规则
说明对各模块的业务。

1.2.4.	算法
详细说明本程序所选用的算法，具体的计算机公式和计算步骤。
	
1.2.5.	数据结构
反映数据元素之间的逻辑关系的数据结构

1.2.6.	用户界面
界面设计，可使用visio中的Windows User Interface制作，但应预先由分析人员与编程人员共同确定统一风格。
以列表方式进行界面元素描述或者文字方式描述输入和输出界面以及要求。

元素名称	元素功能
	

1.3.	模块二设计
1.3.1.	时序图
简述各模块的功能。列明不同角色之间的使用，需要说明权限分配；比如不同用户可以使用特定的菜单功能；应体现各个模块之间的关联关系，时序图，有业务关系的功能模块可以增加数据控制流程图；
1.3.2.	功能描述
包括：
a)	说明本模块的输入/输出参数（命名标识）和调用方式
b)	说明与本模块相直接关联的数据结构（数据库、表文件）。

1.3.3.	功能实现规则
说明对各模块的业务。

1.3.4.	算法
详细说明本程序所选用的算法，具体的计算机公式和计算步骤。
	
1.3.5.	数据结构
反映数据元素之间的逻辑关系的数据结构

1.3.6.	用户界面
界面设计，可使用visio中的Windows User Interface制作，但应预先由分析人员与编程人员共同确定统一风格。
以列表方式进行界面元素描述或者文字方式描述输入和输出界面以及要求。

元素名称	元素功能
	

1.4.	非功能性设计
可从可靠性、易用性、维护性、可移植性等进行描述。



