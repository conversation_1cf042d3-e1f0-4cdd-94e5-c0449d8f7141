<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>变电所辅助监控系统详细设计方案</title>
<script src="https://cdn.jsdelivr.net/npm/echarts@5.3.3/dist/echarts.min.js"></script>
<style>
            :root {
                --font-size-body: 16px;
                --font-size-h5: 16px;       
                --font-size-h4: 18px;    
                --font-size-h3: 20px;     
                --font-size-h2: 24px;    
                --font-size-h1: 28px;    
            }
            body {
                font-feature-settings: 'liga' off, 'clig' off;
                font-family: "SF Pro";
                font-size: var(--font-size-body);
                color: #333B46;
                font-style: normal;
                font-weight: 400;
                line-height: 1.5; 
                margin: 0 24px 0 24px; /* 上 右 下 左 */
                padding: 0px;
            }
            .container {
                max-width: 800px;
                margin: 0px auto;
                padding: 24px 40px;
                background-color: #fff;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                border-radius: 8px;
            }
            .chart-container {
                position: relative;
                margin: 3em auto;
                max-width: 500px;
                height: 400px;
                overflow: visible;
            }

        
        
        h1, h2, h3, h4, h5, h6 {
            font-weight: 600;
            margin-top: 1.5em;
            margin-bottom: 0.8em;
            line-height: 1.3;
        }
        h1 { font-size: 2.2em; border-bottom: 1px solid #eee; padding-bottom: 0.3em;}
        h2 { font-size: 1.8em; border-bottom: 1px solid #eee; padding-bottom: 0.3em;}
        h3 { font-size: 1.5em; }
        h4 { font-size: 1.2em; }
        p {
            margin-bottom: 1.2em;
        }
        ul, ol {
            margin-bottom: 1.2em;
            padding-left: 1.8em;
        }
        li {
            margin-bottom: 0.5em;
        }
        blockquote {
            margin: 1.5em 0;
            padding: 0.5em 1.5em;
            border-left: 4px solid #ddd;
            background-color: #f0f0f0;
            color: #555;
        }
        pre {
            background-color: #2d2d2d;
            color: #f8f8f2;
            padding: 1em;
            overflow-x: auto;
            border-radius: 4px;
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
            font-size: 0.9em;
            margin-bottom: 1.2em;
        }
        code {
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
            background-color: #f0f0f0;
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-size: 0.9em;
        }
        pre code {
            background-color: transparent;
            padding: 0;
            border-radius: 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1.5em;
            font-size: 0.95em;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 0.8em;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
            font-weight: 600;
        }
        img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 1em auto;
        }
        .chart-container {
            width: 100%;
            max-width: 700px; /* Consistent with prompt */
            height: 400px; /* Fixed height for canvas */
            margin: 2em auto;
        }
        .key-points {
            background-color: #eef7ff;
            border: 1px solid #cce0ff;
            padding: 1em;
            margin: 1.5em 0;
            border-radius: 4px;
        }
        .key-points h4 {
            margin-top: 0;
            color: #0052cc;
        }
        .note {
            font-size: 0.9em;
            color: #777;
            margin-top: -0.5em;
            margin-bottom: 1em;
        }
         /* A4-like page width and centering */
         .page-container {
            max-width: 800px; /* Consistent with prompt "A4 paper size (max-width: 800px)" */
            margin: 2rem auto;
            padding: 2rem;
            background: #fff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        @media print {
            
            .page-container {
                box-shadow: none;
                margin: 0;
                padding: 0;
            }
        }
        .table-title {
            font-weight: bold;
            text-align: center;
            margin-bottom: 0.5em;
        }
        .figure-title {
            font-style: italic;
            text-align: center;
            margin-top: 0.5em;
            margin-bottom: 1em;
            color: #555;
        }
    
@media only screen and (max-device-width: 768px) {
            body {
                padding: 0;
                margin: 0;
                font-family: PingFang SC;
                font-size: 15px;
                line-height: 1.5;
            }

            .container {
                padding: 0;
                margin: 16px 20px 30px;
                box-shadow: none;
            }

            h1,
            h2,
            h3,
            h4 {
                font-family: PingFang SC;
            }

            h1 {
                font-size: 1.87em;
                line-height: 1.6;
                margin-bottom: 0.5em;
                text-align: center;
            }

            h2 {
                font-size: 1.6em;
                font-weight: 600;
                margin-top: 1.3em;
                margin-bottom: 0.8em;
                border-bottom: 1px solid #eee;
                padding-bottom: 0.5em;
            }

            h3 {
                font-size: 1.2em;
                font-weight: 600;
                margin-top: 1em;
                margin-bottom: 0.6em;
            }

            h4 {
                font-size: 1.1em;
                font-weight: 500;
                margin-top: 1em;
                margin-bottom: 0.5em;
                font-style: normal;
            }

            h5 {
                font-size: 1em;
                font-weight: 500;
                margin-bottom: 1.2em;
            }

            ul,
            ol {
                font-size: 1em; /* Equivalent to 17.6px if base is 16px */
                font-weight: 400;
                margin-bottom: 1.2em;
                line-height: 1.8;
            }

            p {
                font-size: 1em;
                line-height: 1.8; /* Equivalent to 17.6px if base is 16px */
                font-weight: 400;
                margin-top: 0.8em;
                margin-bottom: 0.8em;
            }

            blockquote {
                padding: 1em 1.2em;

            p {
                margin: 0;
            }
        }

        figcaption {
            margin-top: 0.5em;
            font-size: 0.8em; /* Equivalent to 17.6px if base is 16px */
            font-weight: 400;
            text-align: center;
            font-style: normal;
            color: #7F8896;
        }

        img {
            display: block;
            overflow: hidden;
            max-width: 100%;
            max-height: 335px;
            margin: 1em auto;
            border-radius: 8px;
        }
        ｝</style>
</head>
<body><div class="container">
<div class="page-container">
<h1>变电所辅助监控系统详细设计方案</h1>
<p class="note">当前时间：2025-05-31</p>
<div class="key-points">
<h4>文档撰写策略说明</h4>
<p>本方案采用“全面深化”策略进行撰写，旨在系统性融合《变电所辅助监控系统详细设计》（下称“现有设计文档”）与《数据规范与接口》（下称“数据规范文档”）的内容，对各设计模块进行详细、深入的分析与阐述，确保方案的专业性、规范符合性与可实施性，深度满足用户需求，并严格遵循如Q/CR 1029-2024等行业标准规范。</p>
</div>
<h2><a id="toc-1"></a>1. 引言</h2>
<h3><a id="toc-1-1"></a>1.1 项目背景</h3>
<p>随着铁路和电力行业的飞速发展，对变电所的安全稳定运行提出了更高的要求。传统的变电所运维模式依赖人工巡检，存在效率低、响应慢、难以覆盖全面、特殊环境下作业风险高等问题。变电所辅助监控系统作为提升变电所智能化、自动化运维水平的关键技术手段，通过集成视频监控、环境监测、安全防范、火灾报警、动力照明控制等多种辅助设备，实现对变电所运行状态的全面感知、智能分析和远程控制，对于保障变电所安全运行、提高运维效率、降低运维成本、推进无人化或少人化值守具有至关重要的意义。参考“现有设计文档”1.1节，本系统的建设是行业发展的必然趋势，也是提升变电所本质安全水平的迫切需求。</p>
<h3><a id="toc-1-2"></a>1.2 设计目标与原则</h3>
<p>本变电所辅助监控系统的设计目标旨在构建一个高度集成、智能高效、安全可靠的综合监控平台。具体目标包括：</p>
<ul>
<li><strong>一体化监控：</strong> 实现对变电所内视频监控及巡检、安全防范及门禁、环境监测、火灾报警、动力照明控制等各辅助子系统的统一接入、集中监控和协同管理。</li>
<li><strong>智能化提升：</strong> 运用智能图像识别、自动巡检、智能联动等先进技术，提升系统自动化水平，辅助运维决策，减轻人工负担。</li>
<li><strong>标准化建设：</strong> 严格遵循Q/CR 1029-2024、Q/CR 796-2020等国家及行业相关标准规范，确保系统的规范性和互操作性。</li>
<li><strong>高可靠保障：</strong> 系统关键部件采用冗余设计，确保系统可用率≥99.99%，平均无故障时间（MTBF）≥30000小时（参考“现有设计文档”1.2节）。</li>
<li><strong>实时性响应：</strong> 保证视频控制切换响应时间≤1s，控制命令响应时间≤1s，报警信息实时上报与显示（参考“现有设计文档”1.2节）。</li>
</ul>
<p>系统设计遵循以下核心原则：</p>
<ul>
<li><strong>标准化与开放性：</strong> 采用业界标准的技术和协议，保证系统的互联互通和可扩展性。</li>
<li><strong>集成化与一体化：</strong> 将分散的辅助监控设备和子系统整合到统一平台，实现信息共享和功能联动。</li>
<li><strong>智能化与自动化：</strong> 引入智能分析和自动化流程，提高监控的效率和准确性。</li>
<li><strong>高可靠性与高可用性：</strong> 通过冗余配置、故障自愈等手段，确保系统长期稳定运行。</li>
<li><strong>安全性与保密性：</strong> 采取全面的安全防护措施，保障系统自身安全和数据安全。</li>
<li><strong>易用性与可维护性：</strong> 提供友好的人机交互界面和便捷的系统维护工具。</li>
<li><strong>可扩展性与先进性：</strong> 采用模块化设计，适应未来技术发展和业务需求扩展。</li>
</ul>
<h3><a id="toc-1-3"></a>1.3 设计范围</h3>
<p>本详细设计方案主要覆盖变电所辅助监控系统本身的设计，包括其站控层及间隔层设备的配置、系统软件功能、数据处理、接口规范以及与辅助监控系统主站的交互等。参考“现有设计文档”1.4节，系统主要包含以下子系统和功能模块的详细设计：</p>
<ul>
<li>视频监控及巡检子系统（含智能识别）</li>
<li>安全防范及门禁子系统</li>
<li>环境监测子系统</li>
<li>火灾报警子系统</li>
<li>动力照明控制子系统</li>
<li>系统数据服务与管理</li>
<li>与辅助监控系统主站的各类接口（数据互提、数据采集与控制、视频调阅、视频巡检、图像校核、摄像机鉴权、时钟同步等）</li>
</ul>
<p>本方案不包括辅助监控系统主站的详细设计，但会详细阐述本系统与主站之间的接口定义和交互规范。</p>
<h3><a id="toc-1-4"></a>1.4 主要编写依据</h3>
<p>本详细设计方案的主要编写依据包括但不限于：</p>
<ul>
<li>《变电所辅助监控系统详细设计》（本项目提供的核心参考资料，下称“现有设计文档”）</li>
<li>《数据规范与接口》（本项目提供的核心参考资料，下称“数据规范文档”）</li>
<li>Q/CR 1029-2024《牵引变电所辅助监控系统技术规范》</li>
<li>Q/CR 796-2020《牵引变电所辅助监控系统主站技术规范》</li>
<li>DL/T 634.5104-2002《远动设备及系统 第5-104部分：传输协议 网络任务》</li>
<li>GB/T 28181《公共安全视频监控联网系统信息传输、交换、控制技术要求》</li>
<li>IEC 61850《变电站通信网络和系统》</li>
<li>相关国家、行业关于计算机场地、数据中心、信息安全的标准规范（如GB/T 2887, GB 50174, GB/T 22240等，参考“现有设计文档”附录A.1）。</li>
</ul>
<h3><a id="toc-1-5"></a>1.5 术语与缩略语</h3>
<p>为确保理解一致，本方案中使用的主要术语和缩略语定义如下：</p>
<ul>
<li><strong>辅助监控系统 (AMS)：</strong> Traction Substation Auxiliary Monitoring System，指本方案设计的变电所辅助监控系统。</li>
<li><strong>主站：</strong> 指挥、调度和管理辅助监控系统的上级系统，如段级/局级辅助监控主站。</li>
<li><strong>站控层：</strong> 辅助监控系统中负责数据处理、应用服务、人机交互的层级。</li>
<li><strong>间隔层：</strong> 辅助监控系统中直接连接现场设备的层级，负责数据采集和初步处理。</li>
<li><strong>NVR：</strong> Network Video Recorder，网络硬盘录像机。</li>
<li><strong>IEC104：</strong> IEC 60870-5-104，一种广泛应用于电力系统的远动通信协议。</li>
<li><strong>HTTPS：</strong> Hypertext Transfer Protocol Secure，安全的超文本传输协议。</li>
<li><strong>JSON：</strong> JavaScript Object Notation，一种轻量级的数据交换格式。</li>
<li><strong>API：</strong> Application Programming Interface，应用程序编程接口。</li>
<li><strong>遥信 (YX)：</strong> 反映设备状态的开关量信号。</li>
<li><strong>遥测 (YC)：</strong> 反映设备运行参数的模拟量信号。</li>
<li><strong>遥控 (YK)：</strong> 对设备进行远程操作的命令。</li>
<li><strong>遥调 (YT)：</strong> 对设备的设定值进行远程调整的命令。</li>
<li><strong>预置位：</strong> 摄像机预先设定的监控位置和参数。</li>
<li><strong>巡检：</strong> 按照预定计划或指令对设备进行检查的过程。</li>
<li><strong>智能识别：</strong> 利用人工智能技术对图像、视频等信息进行分析和判断。</li>
<li><strong>SDK：</strong> Software Development Kit，软件开发工具包。</li>
<li><strong>ONVIF：</strong> Open Network Video Interface Forum，开放型网络视频接口论坛，及其制定的标准。</li>
<li><strong>GIS开关柜：</strong> Gas Insulated Switchgear，气体绝缘开关柜。</li>
<li><strong>AIS开关柜：</strong> Air Insulated Switchgear，空气绝缘开关柜。</li>
<li><strong>SF6：</strong> 六氟化硫，一种绝缘气体。</li>
<li><strong>PT：</strong> Potential Transformer，电压互感器。</li>
<li><strong>CT：</strong> Current Transformer，电流互感器。</li>
<li><strong>AT变压器：</strong> Autotransformer，自耦变压器。</li>
<li><strong>SOE：</strong> Sequence of Events，事件顺序记录。</li>
<li><strong>MTBF：</strong> Mean Time Between Failures，平均无故障时间。</li>
<li><strong>MTTR：</strong> Mean Time To Repair，平均修复时间。</li>
</ul>
<h2><a id="toc-2"></a>2. 系统总体设计</h2>
<h3><a id="toc-2-1"></a>2.1 系统概述</h3>
<p>变电所辅助监控系统是一套集成了现代计算机技术、网络通信技术、视频处理技术、智能分析技术以及多种传感器技术的综合性监控平台。其核心价值在于实现对变电所内各类辅助设备（如视频监控设备、安防设备、环境监测传感器、消防报警装置、动力照明设备等）的全面、实时、智能的监控与管理。系统通过对现场设备状态和环境参数的采集、传输、存储、分析和展现，为运维人员提供直观的监控界面和强大的管理工具，支持远程操作、自动巡检、智能告警、联动控制等高级功能，从而有效提升变电所的运行可靠性、运维效率和管理水平，降低人工劳动强度和安全风险。</p>
<h3><a id="toc-2-2"></a>2.2 系统架构设计</h3>
<p>系统架构设计是保障系统功能实现、性能达标和未来扩展的基础。本系统采纳“现有设计文档”第2章的系统总体架构，其核心思想是分层化、服务化、中心化配置和消息驱动。整体架构图如下所示（逻辑示意）。</p>
<div class="figure-title">图2-1 系统逻辑架构示意图</div>
<text (wpf)<="" mjc1iib5psixnjaiihdpzhropsixodaiighlawdodd0inzaiigzpbgw9iinmzmyiihn0cm9rzt0iiznjyyigc3ryb2tllxdpzhropsixii8+phrlehqged0imzy1iib5psixnzuiihrlehqtyw5jag9ypsjtawrkbguipk数据服务="" text="" x="NzUiIHk9IjE5NWgiIHdpZHRoPSIxODAiIGhlaWdodD0iMzAiID5配置、用户、设备、数据、报警等&lt;/text&gt;&lt;rect x="><text (qt="" c++)<="" mjc1iib5psiyndaiihdpzhropsixodaiighlawdodd0inzaiigzpbgw9iinmzmyiihn0cm9rzt0iiznjyyigc3ryb2tllxdpzhropsixii8+phrlehqged0imzy1iib5psiyntuiihrlehqtyw5jag9ypsjtawrkbguip巡检服务="" text="" x="Mjc1IiB5PSIxOTVoIiB3aWR0aD0iMTgwIiBoZWlnaHQ9IjMwIj5IEC104采集、协议转换&lt;/text&gt;&lt;rect x="><text (c++)<="" nzuiihk9ijiznwgiihdpzhropsixodaiighlawdodd0imzaipl识别服务="" text="" x="Mjc1IiB5PSIyNzVoIiB3aWR0aD0iMTgwIiBoZWlnaHQ9IjMwIj5自动巡检执行&lt;/text&gt;&lt;text x="><text (mongodb)<="" (mysql),="" (redis),="" njaiihk9ijmymcigd2lkdgg9ijq4mcigagvpz2h0psi0mcigzmlsbd0ii2y5zwnlziigc3ryb2tlpsijmzmziibzdhjva2utd2lkdgg9ijeilz4+phrlehqged0imzawiib5psiznduiihrlehqtyw5jag9ypsjtawrkbguiigzvbnqtc2l6zt0imtqiigzpbgw9iimzmzmipk实时数据库="" text="" x="NzUiIHk9IjI1NWgiIHdpZHRoPSIxODAiIGhlaWdodD0iMzAiPkAI图像识别&lt;/text&gt;&lt;rect x=" 历史库="" 配置库=""><rect 410)"="" translate(80,="" x="NzAiIHk9IjM3MCIgd2lkdGg9IjQ2MCIgaGVpZ2h0PSIxMTAiIGZpbGw9IiNlN2YyZmYiIHN0cm9rZT0iIzMzMyIgc3Ryb2tlLXdpZHRoPSIxIi8+PHRleHQgeD0iMzAwIiB5PSIzOTAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZvbnQtc2l6ZT0iMTYiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSIjMzMzIj5间隔层 (现场设备)&lt;/text&gt;&lt;g transform="><rect ntuiihk9ijqwiibmb250lxnpemu9ijewiib0zxh0lwfuy2hvcj0ibwlkzgxlij4(摄像头,="" nvr)<="" text="" width="ExMCIgaGVpZ2h0PSI1MCIgcng9IjUiIGZpbGw9IiNGRkYiIHN0cm9rZT0iIzY2NiIvPjx0ZXh0IHg9IjU1IiB5PSIyMCIgZm9udC1zaXplPSIxMiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+视频设备&lt;/text&gt;&lt;text x=" x="MC" y="MC"><g transform="translate(200, 410)"><rect ntuiihk9ijqwiibmb250lxnpemu9ijewiib0zxh0lwfuy2hvcj0ibwlkzgxlij4(门禁,="" text="" width="ExMCIgaGVpZ2h0PSI1MCIgcng9IjUiIGZpbGw9IiNGRkYiIHN0cm9rZT0iIzY2NiIvPjx0ZXh0IHg9IjU1IiB5PSIyMCIgZm9udC1zaXplPSIxMiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+安防设备&lt;/text&gt;&lt;text x=" x="MC" y="MC" 探测器)<=""></rect></g><g transform="translate(320, 410)"><rect ntuiihk9ijqwiibmb250lxnpemu9ijewiib0zxh0lwfuy2hvcj0ibwlkzgxlij4(传感器)<="" text="" width="ExMCIgaGVpZ2h0PSI1MCIgcng9IjUiIGZpbGw9IiNGRkYiIHN0cm9rZT0iIzY2NiIvPjx0ZXh0IHg9IjU1IiB5PSIyMCIgZm9udC1zaXplPSIxMiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+环境设备&lt;/text&gt;&lt;text x=" x="MC" y="MC"></rect></g><g transform="translate(440, 410)"><rect ntuiihk9ijqwiibmb250lxnpemu9ijewiib0zxh0lwfuy2hvcj0ibwlkzgxlij4(消防,="" text="" width="ExMCIgaGVpZ2h0PSI1MCIgcng9IjUiIGZpbGw9IiNGRkYiIHN0cm9rZT0iIzY2NiIvPjx0ZXh0IHg9IjU1IiB5PSIyMCIgZm9udC1zaXplPSIxMiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+其他设备&lt;/text&gt;&lt;text x=" x="MC" y="MC" 动力)<=""></rect></g><path d9im0xnjugmziwig0wic0xnsb2lte1iibzdhjva2u9iimzmzmilz48cgf0acbkpsjtmzy1idmymcbtmcatmtugdi0xntaiihn0cm9rzt0iizmzmyivpjwvzz48l3n2zz4=" alt=" 系统逻辑架构示意图"=""></path>
<h4>2.2.1 分层架构</h4>
<p>系统采用经典的分层架构，通常包括：</p>
<ul>
<li><strong>设备层（间隔层）：</strong> 包含各类现场智能设备、传感器、执行机构等，如摄像头、NVR、门禁控制器、温湿度传感器、SF6传感器、烟感探测器、智能照明控制器、通信管理机等。这些设备负责原始数据的采集和基本控制命令的执行。网络连接上，间隔层设备通常通过工业以太网、RS485总线等方式接入站控层的通信管理机或直接接入站控层网络。</li>
<li><strong>站控层：</strong> 是辅助监控系统的核心，部署在变电所内。主要包括综合应用服务器、数据服务器、视频服务器（或NVR功能集成）、配置管理终端、操作员工作站等。站控层负责：
                <ul>
<li>数据的汇集、处理、存储与转发。</li>
<li>业务逻辑的实现，如报警判断、联动控制、巡检调度、智能分析。</li>
<li>人机交互界面的提供，支持本地监控操作。</li>
<li>与上级主站的通信，上报数据和接收指令。</li>
</ul>
                站控层网络通常采用星型以太网组网，保证数据传输的可靠性和带宽。核心服务器和网络设备会考虑冗余配置。
            </li>
<li><strong>（可选）主站层：</strong> 指段级或局级辅助监控系统主站，通过专用网络与各变电所的辅助监控系统站控层连接，实现远程集中监控、数据分析和统一管理。本方案主要关注站控层及以下，并详细设计与主站层的接口。</li>
</ul>
<h4>2.2.2 核心架构特点</h4>
<p>参考“现有设计文档”2.3节，本系统架构具有以下核心特点：</p>
<ul>
<li><strong>服务化架构：</strong> 核心功能模块化、服务化设计（如后台服务、数据服务、巡检服务、识别服务），各服务可独立部署、升级和维护，提高了系统的灵活性和可伸缩性。</li>
<li><strong>中心化配置管理：</strong> 所有系统配置数据（如设备参数、测点信息、报警阈值、巡检计划等）由后台服务统一管理，各业务服务按需从后台服务获取配置，保证了配置的一致性和管理的便捷性。</li>
<li><strong>消息驱动机制：</strong> 基于Redis等消息中间件实现服务间的异步通信和事件驱动，降低了模块间的耦合度，提升了系统的响应速度和吞吐能力。</li>
<li><strong>标准协议支持：</strong> 对外（如与主站）优先采用行业标准协议如IEC104、HTTPS（遵循“数据规范与接口”文档），对内设备接入支持Modbus、ONVIF、SDK等多种协议，保证了系统的开放性和兼容性。</li>
</ul>
<div class="figure-title">图2-2 系统网络拓扑示意图</div>
<line text="" x1psizntaiihkxpsi0mcigedi9ijm1mcigeti9ijgwiibzdhjva2u9iim1ntuiihn0cm9rzs13awr0ad0imiigc3ryb2tllwrhc2hhcnjhet0insw1ii8+pgrlznm+pg1hcmtlcibpzd0iyxjyb3dozwfkiibtyxjrzxjxawr0ad0imtaiig1hcmtlckhlawdodd0inyigcmvmwd0imcigcmvmwt0imy41iibvcmllbnq9imf1dg8ipjxwyxroigq9ik0wldagtdusmy41iewwldcgwiigzmlsbd0iizu1nsivpjwvbwfya2vypjwvzgvmcz48gsw5lihgxpsizntaiihkxpsi0mcigedi9ijm1mcigeti9ijgwiibzdhjva2u9iim1ntuiihn0cm9rzs13awr0ad0imiigbwfya2vylwvuzd0idxjskcnhcnjvd2hlywqpiiavpjxjb25uzwn0b3jzpjxyzwn0ihg9ijuwiib5psixnjaiihdpzhropsixntaiighlawdodd0injaiihj4psi1iibmawxspsijzmzmiibzdhjva2u9iim5otkilz48dgv4dcb4psixmjuiihk9ije4mcigdgv4dc1hbmnob3i9im1pzgrszsigzm9udc1zaxplpsixmii+综合应用服务器<=""><line text="" x1psixmjuiihkxpsixnjaiihgypsizntaiihkypsixmjaiihn0cm9rzt0iizu1nsigc3ryb2tllxdpzhropsixljuilz48cmvjdcb4psiyntuiihk9ije2mcigd2lkdgg9ije1mcigagvpz2h0psi2mcigcng9ijuiigzpbgw9iinmzmyiihn0cm9rzt0iizy2niivpjx0zxh0ihg9ijmzmciget0imtgwiib0zxh0lwfuy2hvcj0ibwlkzgxliibmb250lxnpemu9ijeyij5数据服务器<=""><line (视频服务器)<="" text="" x1psizmzaiihkxpsixnjaiihgypsizntaiihkypsixmjaiihn0cm9rzt0iizu1nsigc3ryb2tllxdpzhropsixljuilz48cmvjdcb4psi0nzuiihk9ije2mcigd2lkdgg9ije1mcigagvpzgg0psi2mcigcng9ijuiigzpbgw9iinmzmyiihn0cm9rzt0iizy2niivpjx0zxh0ihg9iju1mciget0imtgwiib0zxh0lwfuy2hvcj0ibwlkzgxliibmb250lxnpemu9ijeyij5nvr=""><line text="" x1psi1ntaiihkxpsixnjaiihgypsizntaiihkypsixmjaiihn0cm9rzt0iizu1nsigc3ryb2tllxdpzhropsixljuilz48cmvjdcb4psi1mciget0imjqwiib3awr0ad0imtuwiibozwlnahq9ijywiibyed0insigzmlsbd0ii2zmziigc3ryb2tlpsijnjy2ii8+phrlehqged0imti1iib5psiynjaiihrlehqtyw5jag9ypsjtawrkbguiigzvbnqtc2l6zt0imtiipk操作员工作站<=""><line text="" x1psixmjuiihkxpsiyndaiihgypsizntaiihkypsixmjaiihn0cm9rzt0iizu1nsigc3ryb2tllxdpzhropsixljuilz48cmvjdcb4psiynzuiihk9ijmymcigd2lkdgg9ije1mcigagvpz2h0psi0mcigcng9ijuiigzpbgw9iinlmgyyzmyiihn0cm9rzt0iizrhotbkyyivpjx0zxh0ihg9ijm1mciget0imzq1iib0zxh0lwfuy2hvcj0ibwlkzgxliibmb250lxnpemu9ijeyij5间隔层接入交换机="" 通信管理机<=""><line text="" x1psizntaiihkxpsixmjaiihgypsizntaiihkypsizmjaiihn0cm9rzt0iizu1nsigc3ryb2tllxdpzhropsixljuilz48zyb0cmfuc2zvcm09inryyw5zbgf0zsgwldm4mckipjxyzwn0ihg9ijaiihk9ijaiihdpzhropsixmdaiighlawdodd0intaiihj4psi1iibmawxspsijzmfly2fmiibzdhjva2u9iinjyzgzytuilz48dgv4dcb4psi1mciget0imjuiihrlehqtyw5jag9ypsjtawrkbguiigzvbnqtc2l6zt0imtaipk摄像头<=""><line text="" x1psi1mcigete9ijuwiib4mj0imzuwiib5mj0iltywiibzdhjva2u9iim3nzciihn0cm9rzs13awr0ad0imsivpjxyzwn0ihg9ijeymciget0imcigd2lkdgg9ijewmcigagvpz2h0psi1mcigcng9ijuiigzpbgw9iinmywvjywyiihn0cm9rzt0ii2njyzgzytuilz48dgv4dcb4psixnzaiihk9iji1iib0zxh0lwfuy2hvcj0ibwlkzgxliibmb250lxnpemu9ijewij5安防传感器<=""><line text="" x1psixnzaiihkxpsi1mcigedi9ijm1mcigeti9ii02mcigc3ryb2tlpsijnzc3iibzdhjva2utd2lkdgg9ijeilz48cmvjdcb4psiyndaiihk9ijaiihdpzhropsixmdaiighlawdodd0intaiihj4psi1iibmawxspsijzmfly2fmiibzdhjva2u9iinjyzgzytuilz48dgv4dcb4psiyotaiihk9iji1iib0zxh0lwfuy2hvcj0ibwlkzgxliibmb250lxnpemu9ijewij5环境传感器<=""><line text="" x1psiyotaiihkxpsi1mcigedi9ijm1mcigeti9ii02mcigc3ryb2tlpsijnzc3iibzdhjva2utd2lkdgg9ijeilz48cmvjdcb4psiznjaiihk9ijaiihdpzhropsixmdaiighlawdodd0intaiihj4psi1iibmawxspsijzmfly2fmiibzdhjva2u9iinjyzgzytuilz48dgv4dcb4psi0mtaiihk9iji1iib0zxh0lwfuy2hvcj0ibwlkzgxliibmb250lxnpemu9ijewij5消防设备<=""><line text="" x1psi0mtaiihkxpsi1mcigedi9ijm1mcigeti9ii02mcigc3ryb2tlpsijnzc3iibzdhjva2utd2lkdgg9ijeilz48cmvjdcb4psi0odaiihk9ijaiihdpzhropsixmdaiighlawdodd0intaiihj4psi1iibmawxspsijzmfly2fmiibzdhjva2u9iinjyzgzytuilz48dgv4dcb4psi1mzaiihk9iji1iib0zxh0lwfuy2hvcj0ibwlkzgxliibmb250lxnpemu9ijewij5动力照明<=""><line x1psi1mzaiihkxpsi1mcigedi9ijm1mcigeti9ii02mcigc3ryb2tlpsijnzc3iibzdhjva2utd2lkdgg9ijeilz48l2c+pc9npjwvc3znpg=" alt=" 系统网络拓扑示意图"=""></line>
<p class="note">注：上述架构图和网络拓扑图为高度概括的示意图，实际部署时会根据变电所规模和具体需求进行详细设计。</p>
<h3><a id="toc-2-3"></a>2.3 功能模块划分</h3>
<p>依据“现有设计文档”1.3节、2.4节核心模块架构以及第10章客户端功能模块，本系统主要功能模块（或子系统）划分如下：</p>
<div class="chart-container" id="systemCompositionChart"></div>
<ul>
<li><strong>视频监控及巡检子系统：</strong> 负责实时视频浏览、录像回放、云台控制、预置位管理、自动/手动巡检任务执行、巡检结果分析与上报。</li>
<li><strong>智能识别模块：</strong> 对视频图像进行智能分析，实现设备状态识别（如开关分合、仪表读数）、缺陷检测（如渗油、破损）、环境异常识别（如烟火、异物入侵）、图像智能校核等。</li>
<li><strong>安全防范及门禁子系统：</strong> 负责周界入侵检测、区域布防/撤防、门禁状态监控、出入记录管理、远程门控等。</li>
<li><strong>环境监测子系统：</strong> 实时监测变电所内的温湿度、水浸、SF6气体浓度、气象参数等环境信息，并进行超限报警。</li>
<li><strong>火灾报警子系统：</strong> 监测烟雾、温度等火灾特征参数，接收手动报警信号，实现火灾报警并联动相关消防设备。</li>
<li><strong>动力照明控制子系统：</strong> 监测和控制所内照明设备、风机、水泵、空调等动力设备的运行状态。</li>
<li><strong>红外测温模块：</strong> 通过红外热像仪对关键设备节点进行在线或周期性测温，分析温度异常。</li>
<li><strong>数据服务模块：</strong> 负责与现场设备的数据采集（遵循IEC104、Modbus等协议）、数据解析、数据标准化、点表管理、数据存储与转发、以及与主站的IEC104通信。</li>
<li><strong>系统管理与配置模块：</strong> 包括用户管理、权限管理（含摄像机控制权协调）、系统参数配置、设备配置、点表配置、日志管理、系统维护等功能。</li>
<li><strong>接口服务模块：</strong> 统一管理和提供与辅助监控系统主站及其他第三方系统的接口，主要是基于HTTPS的API接口和IEC104规约接口，严格遵循“数据规范与接口”文档。</li>
<li><strong>客户端应用模块：</strong> 为用户提供图形化的人机交互界面，包括Qt桌面客户端和Web配置管理端，实现数据显示、设备控制、任务管理、报表查询等功能。</li>
</ul>
<h3><a id="toc-2-4"></a>2.4 关键技术选型</h3>
<p>系统的关键技术选型旨在保证系统的先进性、可靠性、开放性和可维护性，具体参考“现有设计文档”第9章技术选型说明：</p>
<ul>
<li><strong>后端服务框架：</strong> 主要采用 <strong>ASP.NET Core Web API (C#)</strong>。
                <ul>
<li>理由：跨平台、高性能、模块化，拥有强大的生态系统和微软的长期支持，适合构建RESTful API服务和复杂的业务逻辑。Entity Framework Core作为ORM框架简化数据库操作。</li>
</ul>
</li>
<li><strong>前端技术栈：</strong>
<ul>
<li>桌面客户端：<strong>Qt (C++)</strong>，版本Qt 5.13或更高。理由：跨平台能力强，性能优异，拥有丰富的UI组件和强大的图形图像处理能力，适合开发功能复杂的监控客户端。</li>
<li>Web配置管理端：<strong>Vue.js (JavaScript)</strong>，版本Vue.js 3.0或更高。理由：轻量级、渐进式框架，组件化开发，上手快，社区活跃，适合构建响应式的Web管理界面。</li>
</ul>
</li>
<li><strong>数据库技术栈：</strong>
<ul>
<li>配置数据库：<strong>MySQL 8.0</strong>。理由：成熟的关系型数据库，稳定性好，开源免费，广泛应用于各类企业级应用。</li>
<li>历史数据库：<strong>MongoDB 4.2</strong>。理由：高性能的NoSQL文档数据库，适合存储大量的时序数据、日志数据、巡检记录等非结构化或半结构化数据，查询灵活。</li>
<li>实时/缓存数据库：<strong>Redis 4.0</strong>。理由：高性能的内存键值数据库，用于实时数据缓存、会话管理、消息队列（Pub/Sub），显著提升系统响应速度。</li>
</ul>
</li>
<li><strong>通信协议：</strong>
<ul>
<li>与主站通信：优先采用 <strong>HTTPS</strong> (承载JSON API，遵循“数据规范与接口”文档) 和 <strong>IEC 60870-5-104 (104规约)</strong>。理由：HTTPS保证数据传输安全和接口标准化；IEC104是电力行业标准远动协议。</li>
<li>视频流传输：<strong>RTSP/RTMP</strong>，设备接入支持 <strong>ONVIF、GB/T 28181</strong> 及主流厂家SDK（如海康SDK、大华SDK）。理由：兼顾标准协议的开放性和私有SDK的设备兼容性与性能。</li>
<li>设备层通信：<strong>Modbus (TCP/RTU)</strong>、OPC UA、自定义协议等。理由：Modbus是工业领域常用协议，OPC UA提供更安全的面向服务架构。</li>
</ul>
</li>
<li><strong>智能识别组件：</strong>
<ul>
<li>图像处理库：<strong>OpenCV</strong>。理由：功能强大的开源计算机视觉库，提供丰富的图像处理和分析算法。</li>
<li>深度学习框架：<strong>PyTorch</strong> or TensorFlow。理由：主流的深度学习框架，支持模型训练和推理，用于实现复杂的图像识别任务。</li>
</ul>
</li>
<li><strong>消息中间件：</strong> <strong>Redis Pub/Sub</strong> 或 RabbitMQ/Kafka。理由：Redis Pub/Sub轻量易用，满足大部分系统内消息通知需求；若对消息可靠性有更高要求，可考虑RabbitMQ或Kafka。</li>
</ul>
<div class="key-points">
<h4>关键要点总结：系统总体设计</h4>
<ul>
<li>系统采用分层（设备层、站控层）、服务化架构，以ASP.NET Core为核心后端，Qt/Vue.js为前端，MySQL/MongoDB/Redis为数据存储。</li>
<li>功能模块全面，覆盖视频、安防、环境、消防、动力、巡检、智能识别等，并强调与主站的标准化接口。</li>
<li>技术选型兼顾成熟度、性能、开放性和行业标准（如IEC104, HTTPS, ONVIF）。</li>
<li>中心化配置和消息驱动是提升系统灵活性和效率的重要机制。</li>
</ul>
</div>
<h2><a id="toc-3"></a>3. 模块详细设计</h2>
<p>本章节将对系统的核心功能模块/子系统进行深入、细致的设计描述。每个模块的设计都将严格参照“现有设计文档”的相关章节，并深度融合“数据规范与接口”文档中的具体要求。</p>
<h3><a id="toc-3-1"></a>3.1 视频监控及巡检子系统</h3>
<p>本子系统是辅助监控系统的核心组成部分，负责对变电所内重要区域和设备进行实时可视化监控和自动化/智能化巡检。设计参考“现有设计文档”10.4视频监控模块、10.5智能巡检模块。</p>
<h4>3.1.1 功能描述</h4>
<ul>
<li><strong>实时视频浏览：</strong>
<ul>
<li>支持多分屏显示（1、4、9、16等自定义布局）。</li>
<li>云台控制（PTZ）：包括方向控制（上、下、左、右、左上、右上、左下、右下）、变倍、聚焦、光圈调节。</li>
<li>预置位调用与设置：快速调用已定义的摄像机预置位，并支持用户根据权限设置新的预置位。预置位的配置和调用严格遵循“数据规范与接口”文档中 E.2.5.9（摄像机预置位点表）、E.4.3（调用移动巡检设备预置位）、E.4.5（设置摄像机预置位）的要求。</li>
<li>主子码流切换：根据网络带宽和显示需求，灵活切换主码流（高清）和子码流（标清）。</li>
<li>图像抓拍与手动录像：支持对当前视频画面进行即时抓拍并保存，支持手动启动/停止录像。</li>
</ul>
</li>
<li><strong>录像回放与管理：</strong>
<ul>
<li>按时间、摄像机、事件等多条件检索历史录像。</li>
<li>提供播放控制功能（播放、暂停、快进、慢放、逐帧、停止）。
                    <li>支持多路视频同步回放。</li>
<li>支持录像片段下载和导出。</li>
</li></ul>
</li>
<li><strong>巡检功能：</strong>
<ul>
<li><strong>自动巡检任务管理：</strong> 运维人员可根据预设模板或自定义配置巡检计划（包括巡检点、巡检路径、巡检周期、停留时间、拍照张数等）。系统按计划自动执行巡检任务，调用摄像机（固定或导轨式）到指定预置位进行拍照、录像和数据采集。巡检计划的下发与管理严格遵循“数据规范与接口”E.5.3.1。</li>
<li><strong>手动/临时巡检：</strong> 支持运维人员发起即时巡检任务，手动控制摄像机或导轨机器人进行巡检，并记录巡检过程。临时巡检任务的下发遵循“数据规范与接口”E.5.3.2。</li>
<li><strong>巡检过程控制：</strong> 主站可对正在执行的巡检任务进行启动、暂停、继续、终止等控制，遵循“数据规范与接口”E.5.3.3。</li>
<li><strong>巡检进度与结果上报：</strong> 系统在巡检过程中实时上报巡检进度，巡检完成后上报详细的巡检结果（包括图片、视频片段、识别结果、测温数据等）至主站。遵循“数据规范与接口”E.5.3.4（上传进度）和E.5.3.5（上传结果）。若上报失败，主站可通过E.5.3.6接口召唤未上传的巡检结果。</li>
<li>巡检记录与分析：存储巡检历史记录，提供查询、统计和分析功能，生成巡检报告。</li>
</ul>
</li>
<li><strong>摄像机配置管理：</strong>
<ul>
<li>摄像机接入与参数配置，支持固定枪机、云台枪机、球机、热成像摄像机、双光谱摄像机、导轨式巡检摄像机等。</li>
<li>**供电设备命名规范：** 系统内所有供电设备的命名，特别是在摄像机关联目标设备时，严格遵循“数据规范与接口”E.2.1（表E.1）的规定，如“线路名称_所亭名称_运行编号_设备类型名称”。例如，在配置摄像机预置位点表（E.2.5.9）中的“目标设备名称”字段时，若拍摄单个设备，则填写符合此规范的供电设备名称。</li>
<li>**摄像机拍摄焦点命名规范：** 摄像机预置位名称的制定，严格遵循“数据规范与接口”E.2.4（表E.4）的规则，格式为“供电设备运行编号-拍摄焦点中文名称”。例如，“101-全景”，“1B-油位计”。这确保了预置位与实际监控意图的一致性。</li>
<li>**摄像机图像显示规范：** 视频画面叠加信息（OSD）严格遵循“数据规范与接口”E.2.2（表E.2）的要求：当前时间（左上角）、摄像机名称（右上角）、预置位名称（右下角）、所亭名称（左下角）。</li>
</ul>
</li>
<li><strong>摄像机鉴权机制：</strong>
<ul>
<li>系统与主站控制摄像机（云台转动、预置位调用等）前，均需向鉴权中心申请操作权限。权限申请、释放、变更通知等流程严格遵循“数据规范与接口”E.7的规定，包括E.7.1.1中定义的权限级别（表E.46）。</li>
</ul>
</li>
</ul>
<h4>3.1.2 处理流程 (示例：自动巡检任务)</h4>
<div class="figure-title">图3-1-1 自动巡检任务处理流程示意图</div>
<text (可选)调用智能识别<="" mtawiib5psi0mduiihrlehqtyw5jag9ypsjtawrkbguipjyu="" text="" x="MTAwIiB5PSIzMTUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPjIu 等待停留时间&lt;/text&gt;&lt;path d9Im0xMDAgMzI1IG0wIDIwIiBtYXJrZXItZW5kPSJ1cmwoI2Fycm93aGVhZCkiIHN0cm9rZT0iIzMzMyIvPjxyZWN0IHg9IjEwIiB5PSIzNjUiIHdpZHRoPSIxODAiIGhlaWdodD0iNzAiIHJ4PSI1IiBmaWxsPSIjZjJlNmZmIiBzdHJva2U9IiMzY2NhIi8+PHRleHQgeD0iMTAwIiB5PSIzODUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPjUu 图像采集/拍照&lt;/text&gt;&lt;text x="><path d9im0xmdagndm1ig0widiwiibtyxjrzxitzw5kpsj1cmwoi2fycm93agvhzckiihn0cm9rzt0iizmzmyivpjxyzwn0ihg9ijewiib5psi0nzuiihdpzhropsixodaiighlawdodd0intaiihj4psi1iibmawxspsijzjjlnmzmiibzdhjva2u9iimzy2nhii8+phrlehqged0imtawiib5psi0otuiihrlehqtyw5jag9ypsjtawrkbguipjyu="" text="" 上传巡检进度<=""><text mjqwiib5psi1ntuiihdpzhropsixodaiighlawdodd0indaiihj4psi1iibmawxspsijzjjlnmzmiibzdhjva2u9iimzy2nhii8+phrlehqged0imzmwiib5psi1nzuiihrlehqtyw5jag9ypsjtawrkbguipjgu="" text="" x="MTAwIiB5PSI1MTUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPmto 主站 (若巡检点完成)&lt;/text&gt;&lt;path d9Im0yMDAgMzA1IGw1MCAwIGwwIDIwMCBsLTUwIDAiIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzk5OSIgc3Ryb2tlLWRhc2hhcnJheT0iMywzIi8+PHRleHQgeD0iMjMzIiB5PSI0MTUiIHRyYW5zZm9ybT0icm90YXRlKDkwIDI1MyA0MTUpIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LXNpemU9IjEwIj5循环所有巡检点&lt;/text&gt;&lt;line x1PSIxMDAiIHkxPSI1MjUiIHgyPSIxMDAiIHkyPSI1NDUiIHN0cm9rZT0iIzMzMyIgbWFya2VyLWVuZD0idXJsKCNhcnJvd2hlYWQpIi8+PGRpYW1vbmQgcG9pbnRzPSIxMDAsNTU1IDUxMCw1NzUgMTAwLDU5NSA5MCw1NzUiIHN0cm9rZT0iIzNjY2EiIGZpbGw9IiNlYmY4ZmIiIHN0cm9rZS13aWR0aD0iMS41Ii8+PHRleHQgeD0iMTAwIiB5PSI1NzUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPjcu 所有巡检点完成?&lt;/text&gt;&lt;line x1PSIxOTYiIHkxPSI1NzUiIHgyPSIyMzYiIHkyPSI1NzUiIHN0cm9rZT0iIzMzMyIgbWFya2VyLWVuZD0idXJsKCNhcnJvd2hlYWQpIi8+PHRleHQgeD0iMjE2IiB5PSI1NzAIIGZvbnQtc2l6ZT0iMTAiPk是&lt;/text&gt;&lt;line x1PSIxMDAiIHkxPSIzMjUiIHgyPSItNTAiIHkyPSIzMjUiIHN0cm9rZT0iIzMzMyIvPjxwYXRoIGQ9Ik0tNTAgMzI1IGwtNTAgMjUwIiBzdHJva2U9IiMzMzMiLz48cGF0aCBkPSJtLTUwIDU3NSBsNjAgMCIgc3Ryb2tlPSIjMzMzIiBtYXJrZXItZW5kPSJ1cmwoI2Fycm93aGVhZCkiLz48dGV4dCB4PSItMjUiIHk9IjQ1MCIgdHJhbnNmb3JtPSJyb3RhdGUoLTI3MCAtMjUgNDUwKSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1zaXplPSIxMCI+否&lt;/text&gt;&lt;rect x=" 上传巡检结果至主站<="">" alt="自动巡检任务处理流程示意图"/&gt;
        <ol>
<li><strong>主站下发巡检计划/任务：</strong> 主站通过“数据规范与接口”中 E.5.3.1 (下发巡检计划) 或 E.5.3.2 (下发临时巡检任务) 接口，将包含巡检点位、摄像机名称、预置位编号、停留时间、拍照张数等信息的巡检任务下发给辅助监控系统。</li>
<li><strong>辅助监控系统接收与解析：</strong> 辅助监控系统（通常是巡检服务模块）接收到HTTPS请求，校验Token，解析JSON报文，提取巡检任务详情并存储。</li>
<li><strong>任务调度执行：</strong> 根据任务类型（周期或立即）和启动时间，巡检服务启动任务执行。系统需支持多任务并行巡检。</li>
<li><strong>遍历巡检点：</strong> 依次处理任务中的每个巡检点位信息。
                <ol type="a">
<li><strong>调用预置位/控制云台：</strong> 根据巡检点配置的摄像机名称和预置位编号，通过 E.4.3 (调用移动巡检设备预置位，如导轨机器人) 或直接控制固定摄像机云台，使其定位到目标预置位。此过程可能需要通过 E.7 (摄像机鉴权接口) 申请控制权。</li>
<li><strong>停留与稳定：</strong> 摄像机到达预置位后，按照配置的停留时间（`residenceTime`，优先采用巡检点位中的，其次任务中的，再次系统默认，不应小于5秒）进行等待，确保画面稳定。</li>
<li><strong>图像采集/拍照：</strong> 执行拍照指令，获取指定张数（`pictureNum`）的图片。若为红外测温任务，则同时采集红外热像图和可见光图。</li>
<li><strong>(可选) 调用智能识别：</strong> 若巡检点配置了智能识别项（如表计读数、开关状态），则将采集的图像发送给智能识别模块进行分析。</li>
<li><strong>上传巡检进度：</strong> 每完成一个巡检点（或根据策略），通过“数据规范与接口”E.5.3.4 (上传巡检进度) 接口向主站上报当前巡检点的信息（巡检任务编号、序号、摄像机名称、预置位编号等）。</li>
</ol>
</li>
<li><strong>判断任务完成：</strong> 检查是否所有巡检点均已执行完毕。
                <ul>
<li><strong>否：</strong> 返回步骤4，继续下一个巡检点。</li>
<li><strong>是：</strong> 进入步骤6。</li>
</ul>
</li>
<li><strong>上传巡检结果：</strong> 巡检任务全部完成后，整理所有巡检点的执行结果（包括拍照时间 `captureTime`、实际照片数量 `pictureNum`、照片文件 `pics`、识别结果 `result`、执行状态 `status`、结果描述 `message`）。通过“数据规范与接口”E.5.3.5 (上传巡检结果)接口，将结果以 `multipart/form-data` 格式分批（若数据量大，每包不超过256MB，中间包`endTime`为空，最后包有值）上传至主站。上传时需包含巡检计划信息（任务名称、编号、开始结束时间等）。系统应具备重传机制。</li>
</ol>
<h4>3.1.3 接口设计</h4>
<p>本子系统涉及的接口主要分为与主站的接口和与内部模块/设备的接口。</p>
<ul>
<li><strong>与主站的核心接口 (HTTPS/IEC104，严格遵循“数据规范与接口”文档)：</strong>
<ul>
<li>基础数据：E.2.5.9 (摄像机预置位点表召唤)。</li>
<li>视频调阅与控制：E.4.2 (调阅视频 - 通过NVR，GB/T 28181/ONVIF)，E.4.3 (调用移动巡检设备预置位)，E.4.4 (获取移动巡检设备当前位置)，E.4.5 (设置摄像机预置位)。</li>
<li>视频巡检：E.5 (全套接口：E.5.3.1至E.5.3.6)。</li>
<li>摄像机鉴权：E.7 (全套接口：E.7.1.2至E.7.1.4)。</li>
<li>部分报警信息（如视频分析发现的缺陷预警）可能通过E.3.2的104规约上送。</li>
</ul>
</li>
<li><strong>与内部模块/设备的接口：</strong>
<ul>
<li>与NVR/摄像头：通过厂家SDK、ONVIF或GB/T 28181协议进行视频流获取、云台控制、预置位调用/设置、录像控制等。具体参数和命令格式参照相应协议规范。</li>
<li>与智能识别模块：传递待识别的图像/视频流，接收识别结果。通常为内部API调用（如HTTP或RPC）。</li>
<li>与数据服务模块/后台服务：获取设备配置信息、巡检计划；上报巡检结果（若不直接上送主站）、状态信息；存储巡检记录和报警信息到数据库。</li>
<li>与鉴权中心（若独立部署）：进行摄像机控制权申请与释放，遵循E.7接口规范。</li>
</ul>
</li>
</ul>
<p><strong>示例：调用移动巡检设备预置位接口 (参考“数据规范与接口”E.4.3)</strong></p>
<ul>
<li><strong>数据方向：</strong> 铁路供电调度控制系统主站 → 辅助监控系统</li>
<li><strong>接口提供方：</strong> 辅助监控系统</li>
<li><strong>接口路径：</strong> `https://ip:port/项目路径/api/callPreset`</li>
<li><strong>接口说明：</strong> 采用HTTPS协议，POST方式提交。</li>
<li><strong>数据格式：</strong> body采用JSON字符串，header需包含token字符串。</li>
<li><strong>请求数据结构 (表E.20)：</strong>
<pre><code class="json">
{
  "subName": "XX变电所",
  "camName": "高压室_顶部_导轨",
  "presetCode": "2"
}
                </code></pre>
</li>
<li><strong>返回数据结构 (表E.21)：</strong>
<pre><code class="json">
{
  "status": 0, // 0成功 1失败
  "message": "调用成功"
}
                </code></pre>
</li>
</ul>
<h4>3.1.4 数据结构与数据模型设计</h4>
<p>本子系统核心数据结构和模型设计需严格遵循“数据规范与接口”及“现有设计文档”第3章。</p>
<ul>
<li><strong>摄像机预置位点表 (核心)：</strong> 基于“数据规范与接口”表E.18设计。关键字段包括：所亭名称、目标设备名称、摄像机名称（取自辅助监控设备配置表）、预置位编号、预置位名称（规则：供电设备运行编号-拍摄焦点中文名称）。
                <p class="table-title">表3-1-1 摄像机预置位点表 (E.18摘要)</p>
<table>
<thead><tr><th>字段名称</th><th>填写要求</th><th>样例</th></tr></thead>
<tbody>
<tr><td>序号</td><td>作统计数量用，以1、2、3…顺编。</td><td>1</td></tr>
<tr><td>所亭名称</td><td>必填，与变配电所基本情况表的所亭名称一致。</td><td>XX变电所</td></tr>
<tr><td>目标设备名称</td><td>当预置位拍摄单个设备时，填供电设备名称或辅助设备名称；当拍摄多个设备（如全景）时不填。</td><td>101_断路器 / (空)</td></tr>
<tr><td>摄像机名称</td><td>必填，取值为“辅助监控设备配置表”中的“设备名称”。</td><td>进线区_东侧龙门柱_球机</td></tr>
<tr><td>预置位编号</td><td>必填，数字1-65535。固定式枪机：不填。</td><td>23</td></tr>
<tr><td>预置位名称</td><td>必填，命名规则：供电设备运行编号-拍摄焦点中文名称。拍摄焦点中文名称与摄像机拍摄焦点命名规范中的名称一致。</td><td>101-A相进线线夹</td></tr>
</tbody>
</table>
<p>此表由辅助监控系统厂商提供，并通过E.2.5.9定义的接口由主站召唤。</p>
</li>
<li><strong>辅助监控设备配置表 (视频设备部分)：</strong> 基于“数据规范与接口”表E.7设计。关键字段包括：所亭名称、安装区域、位置描述、设备大类（视频）、设备子类（枪机、云台枪机、球机、热成像枪机等）、双光谱摄像机通道（A/B）、设备名称（自动生成：安装区域_位置描述_设备子类+双光谱通道）。
                <p class="table-title">表3-1-2 视频设备配置表 (E.7摘要)</p>
<table>
<thead><tr><th>字段名称</th><th>填写要求</th><th>样例</th></tr></thead>
<tbody>
<tr><td>所亭名称</td><td>必填</td><td>XX变电所</td></tr>
<tr><td>安装区域</td><td>必填，与E.2.3规范一致</td><td>主变区</td></tr>
<tr><td>位置描述</td><td>必填，区域内不重复</td><td>1号主变北侧</td></tr>
<tr><td>设备大类</td><td>必填，视频</td><td>视频</td></tr>
<tr><td>设备子类</td><td>必填，如球机、热成像球机</td><td>球机</td></tr>
<tr><td>双光谱摄像机通道</td><td>非必填，A:可见光 B:热成像</td><td>A</td></tr>
<tr><td>设备名称</td><td>必填，自动生成</td><td>主变区_1号主变北侧_球机A</td></tr>
</tbody>
</table>
</li>
<li><strong>巡检任务模型：</strong> 参考“现有设计文档”3.2.5，并结合“数据规范与接口”E.5中巡检计划/任务/点位信息（表E.27-E.29, E.31-E.32, E.39等）的字段。
                <ul>
<li>巡检计划/任务主表：任务编号(code)、任务名称(name)、所亭名称(subName)、主站类型(centerType)、停留时间(residenceTime)、周期信息(weeks, startDate, startTime)。</li>
<li>巡检点位表：巡检序号(seq)、摄像机名称(camName)、预置位编号(presetCode)、拍照张数(pictureNum)、停留时间(residenceTime)。</li>
<li>巡检结果表：关联巡检点，存储拍照时间(captureTime)、照片文件(pics)、识别结果(result)、执行状态(status)、消息(message)。</li>
</ul>
</li>
<li><strong>视频设备模型：</strong> 参考“现有设计文档”3.2.6，包含设备基本信息（IP、端口、用户名等）和预置位信息。预置位信息需与上述“摄像机预置位点表”保持一致。</li>
</ul>
<h4>3.1.5 关键算法/逻辑说明</h4>
<ul>
<li><strong>巡检路径优化：</strong> 对于导轨式巡检机器人，可根据巡检点位地理位置信息，采用如最短路径算法（如Dijkstra或A*）或根据设备重要性加权的路径规划算法，优化巡检效率。</li>
<li><strong>预置位停留时间逻辑：</strong> 严格遵循“数据规范与接口”E.5.3.1注和E.5.3.2注中的优先级：优先采用巡检点位中的停留时间，若为空，则采用巡检计划/任务中的参数，若均为空，则采用辅助监控系统中的默认参数（不小于5秒）。</li>
<li><strong>巡检结果分包上传逻辑：</strong> 遵循“数据规范与接口”E.5.3.5 g) h) i)的要求，大数据量分批上传，每包不超过256MB，中间包`endTime`为空，最后包有值，按时间顺序串行上传。</li>
</ul>
<h4>3.1.6 异常处理机制</h4>
<ul>
<li><strong>摄像机/NVR通信中断：</strong> 系统应能检测到设备离线状态，在界面上告警，并记录事件。自动巡检任务执行时若设备离线，应标记该巡检点失败，并在任务报告中注明原因。</li>
<li><strong>预置位调用失败：</strong> 若调用预置位失败（如设备响应超时、预置位不存在），记录错误日志，巡检任务中标记该点失败。</li>
<li><strong>巡检任务接口调用失败：</strong>
<ul>
<li>下发任务失败：主站应有重试机制或记录失败。辅助系统若返回失败，主站应能处理。</li>
<li>上传进度/结果失败：辅助监控系统自身应具备重传机制。如“数据规范与接口”E.5.3.5所述，若与段级辅助监控区网络中断或段级返回失败，网络恢复后应再次上送数据。主站也可通过E.5.3.6召唤巡检结果。</li>
</ul>
</li>
<li><strong>摄像机控制权冲突：</strong> 遵循“数据规范与接口”E.7.1.2 g) h)的逻辑。若申请的控制权级别低于正在被占用的级别，申请失败并返回预计释放时间。</li>
<li><strong>存储空间不足：</strong> 对于录像和巡检图片存储，应有存储空间预警机制，达到阈值时提示清理或自动覆盖旧数据（根据配置策略）。</li>
</ul>
<h3><a id="toc-3-2"></a>3.2 智能识别模块</h3>
<p>智能识别模块利用计算机视觉和人工智能技术，对采集的视频图像进行分析，提取关键信息，辅助运维人员判断设备状态、发现潜在缺陷。设计参考“现有设计文档”10.11智能识别模块。</p>
<h4>3.2.1 功能描述</h4>
<ul>
<li><strong>设备状态识别：</strong>
<ul>
<li><strong>开关位置识别：</strong> 自动识别断路器、隔离开关的分合闸状态。其识别结果可用于与SCADA系统上送的遥信状态进行对比校核（见“数据规范与接口”E.6 图像智能校核接口）。摄像机需对准“数据规范与接口”E.2.4中定义的开关“指示”、“触指”等焦点。</li>
<li><strong>指示灯状态识别：</strong> 识别设备面板上各种指示灯的颜色（红、绿、黄等）和状态（亮、灭、闪烁）。</li>
<li><strong>仪表读数识别：</strong> 自动读取指针式或数字式仪表的数值（如压力表、油位计、温控器表计等）。摄像机需对准“数据规范与接口”E.2.4中定义的“表计”、“油位计”等焦点。</li>
<li>设备编号/铭牌识别：利用OCR技术识别设备本体或铭牌上的编号、型号等文字信息。</li>
</ul>
</li>
<li><strong>设备缺陷检测：</strong>
<ul>
<li>渗油、漏油检测：识别变压器、充油套管等设备的油渍、油迹。</li>
<li>破损、裂纹检测：识别绝缘子、设备外壳等部件的破损、裂纹。</li>
<li>变形、倾斜检测：识别支柱、构架等结构的异常变形或倾斜。</li>
<li>锈蚀、腐蚀检测：识别金属部件的锈蚀情况。</li>
<li>异物检测：识别设备本体或周边区域是否存在鸟巢、塑料袋、树枝等异物。</li>
</ul>
</li>
<li><strong>环境异常识别：</strong>
<ul>
<li>烟雾、火焰检测：通过视频分析算法实时检测监控区域内的烟雾和火焰迹象，辅助火灾报警。</li>
<li>人员入侵检测：结合周界防范，在非工作时间或禁止区域检测到人员活动。</li>
<li>积水检测：识别设备区域的积水情况。</li>
</ul>
</li>
<li><strong>图像智能校核：</strong> 配合“数据规范与接口”E.6，当断路器或隔离开关发生变位后，辅助监控系统主站可请求辅助监控系统进行图像智能校核。辅助监控系统调用摄像机（指定摄像机名称 `camName` 和预置位编号 `presetCode`）对该设备进行拍摄和状态识别，并将校核值（0-分, 1-合, 2-接地, 3-未知, 4-异常）返回给主站。</li>
</ul>
<h4>3.2.2 处理流程 (示例：开关变位智能校核)</h4>
<div class="figure-title">图3-2-1 开关变位智能校核流程示意图</div>
<path d9im0xmtagnjagdjiwiibtyxjrzxitzw5kpsj1cmwoi2fycm93agvhzdipiibzdhjva2u9iimzmzmilz48cmvjdcb4psixmciget0imtawiib3awr0ad0imjawiibozwlnahq9ijuwiibyed0insigzmlsbd0ii2u2zmnmziigc3ryb2tlpsijodrhm2rmii8+phrlehqged0imtewiib5psixmjuiihrlehqtyw5jag9ypsjtawrkbguipjiu="" text="" 主站向辅助系统发送校核请求<=""><text (返回="" mtexiib5psiymziiihrlehqtyw5jag9ypsjtawrkbguiigzvbnqtc2l6zt0imtaipi="" status="0)&lt;/text" x="MTExIiB5PSIxNDIiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZvbnQtc2l6ZT0iMTAiPi (接口 E.6.1.1)&lt;/text&gt;&lt;path d9Im0xMTAgMTUwIHYyMCIgbWFya2VyLWVuZD0idXJsKCNhcnJvd2hlYWQyKSIgc3Ryb2tlPSIjMzMzIi8+PHJlY3QgeD0iMTAiIHk9IjE5MCIgd2lkdGg9IjIwMCIgaGVpZ2h0PSI1MCIgcng9IjUiIGZpbGw9IiNmMmU2ZmYiIHN0cm9rZT0iIzNjY2EiLz48dGV4dCB4PSIxMTAiIHk9IjIxNSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+My4 辅助系统接收请求并响应&lt;/text&gt;&lt;text x="><path d9im0xmtagmjqwihyymcigbwfya2vylwvuzd0idxjskcnhcnjvd2hlywqyksigc3ryb2tlpsijmzmzii8+pnjly3qged0imtaiihk9iji4mcigd2lkdgg9ijiwmcigagvpz2h0psi3mcigcng9ijuiigzpbgw9iinmmmu2zmyiihn0cm9rzt0iiznjy2eilz48dgv4dcb4psixmtaiihk9ijmwmcigdgv4dc1hbmnob3i9im1pzgrszsi+nc4="" text="" 辅助系统：<=""><text b.="" mtexiib5psizndaiihrlehqtyw5jag9ypsjtawrkbguiigzvbnqtc2l6zt0imteipi="" text="" x="MTExIiB5PSIzMjAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZvbnQtc2l6ZT0iMTEiPi   a. 调用摄像机到预置位&lt;/text&gt;&lt;text x=" 图像采集与智能识别<=""><path d9im0xmtagmzuwihyymcigbwfya2vylwvuzd0idxjskcnhcnjvd2hlywqyksigc3ryb2tlpsijmzmzii8+phjly3qged0imtaiihk9ijm5mcigd2lkdgg9ijiwmcigagvpz2h0psi1mcigcng9ijuiigzpbgw9iinmmmu2zmyiihn0cm9rzt0iiznjy2eilz48dgv4dcb4psixmtaiihk9ijqxnsigdgv4dc1hbmnob3i9im1pzgrszsi+ns4="" text="" 辅助系统向主站发送校核结果<=""><text (e.g.="" nte1iib5psiymziiihrlehqtyw5jag9ypsjtawrkbguiigzvbnqtc2l6zt0imtaipi="" text="" x="MTExIiB5PSI0MzIiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZvbnQtc2l6ZT0iMTAiPi (接口 E.6.1.2)&lt;/text&gt;&lt;path d9Im0yMTQgMjE1IGwyMDAgMCIgc3Ryb2tlPSIjMzMzIiBtYXJrZXItZW5kPSJ1cmwoI2Fycm93aGVhZDIpIi8+PHJlY3QgeD0iNDE1IiB5PSIxOTAiIHdpZHRoPSIyMDAiIGhlaWdodD0iNTAiIHJ4PSI1IiBmaWxsPSIjZmZlZGRjIiBzdHJva2U9IiNjY2IyMDAiLz48dGV4dCB4PSI1MTUiIHk9IjIxNSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+Ni4 主站接收并处理校核结果&lt;/text&gt;&lt;text x=" 对比遥信)<="">" alt="开关变位智能校核流程示意图"/&gt;
        <ol>
<li><strong>主站检测到开关变位：</strong> 辅助监控系统主站（通常是SCADA系统）监测到所内某断路器或隔离开关发生遥信变位（SOE事件）。</li>
<li><strong>主站发送校核请求：</strong> 主站调用辅助监控系统提供的“请求开关变位智能校核”接口（“数据规范与接口”E.6.1.1，路径 `api/pscada/checkRequest`），请求中包含所亭名称 (`subName`)、发生变位的设备运行编号 (`equipCode`)、用于校核的摄像机名称 (`camName`)、预置位编号 (`presetCode`) 以及遥信变位时间 (`soeTime`)。</li>
<li><strong>辅助系统接收请求并初步响应：</strong> 辅助监控系统接收到请求后，进行参数校验。若校验通过，向主站返回成功的状态（`status: 0`），表示已接收请求，将进行处理。</li>
<li><strong>辅助系统执行智能校核：</strong>
<ol type="a">
<li>调用视频监控模块，控制指定的摄像机 (`camName`) 转到指定的预置位 (`presetCode`)。此过程可能涉及通过E.7接口申请摄像机控制权。</li>
<li>摄像机到位后，进行图像采集。</li>
<li>调用智能识别算法，对采集到的图像进行分析，判别开关的当前实际状态（分、合、接地等）。</li>
</ol>
</li>
<li><strong>辅助系统发送校核结果：</strong> 智能识别完成后，辅助监控系统调用主站提供的“接收开关变位智能校核结果”接口（“数据规范与接口”E.6.1.2，路径 `api/pscada/checkResult`），将校核结果 (`checkValue`)，连同原始请求中的设备信息和变位时间，发送给主站。</li>
<li><strong>主站接收并处理校核结果：</strong> 主站接收到校核结果后，可将其与遥信状态进行对比，用于辅助判断遥信的准确性或设备实际状态。主站向辅助系统返回接收状态（`status: 0`表示成功处理）。</li>
</ol>
<h4>3.2.3 接口设计</h4>
<ul>
<li><strong>与主站的核心接口 (HTTPS，严格遵循“数据规范与接口”文档 E.6)：</strong>
<ul>
<li><strong>请求开关变位智能校核 (E.6.1.1)：</strong> 主站 → 辅助系统。
                        <ul>
<li>路径: `/api/pscada/checkRequest`。</li>
<li>请求参数: `subName`, `equipCode`, `camName`, `presetCode`, `soeTime`。</li>
<li>响应参数: `status`, `message`。</li>
</ul>
</li>
<li><strong>接收开关变位智能校核结果 (E.6.1.2)：</strong> 辅助系统 → 主站。
                        <ul>
<li>路径: `/api/pscada/checkResult`。</li>
<li>请求参数: `subName`, `equipCode`, `camName`, `presetCode`, `soeTime`, `checkValue`。</li>
<li>响应参数: `status`, `message`。</li>
</ul>
</li>
</ul>
</li>
<li><strong>与内部模块的接口：</strong>
<ul>
<li>与视频监控模块：接收来自该模块的实时视频流或抓拍图像用于分析。</li>
<li>与后台服务/数据服务：获取设备信息、摄像机与预置位配置；将识别结果（如缺陷、异常）存入数据库或通过消息队列发布。</li>
</ul>
</li>
</ul>
<h4>3.2.4 数据结构与数据模型设计</h4>
<ul>
<li><strong>识别结果数据模型：</strong>
<p class="table-title">表3-2-1 智能识别结果数据模型 (示例)</p>
<table>
<thead><tr><th>字段名称</th><th>数据类型</th><th>说明</th><th>样例</th></tr></thead>
<tbody>
<tr><td>ResultID</td><td>String</td><td>识别结果唯一标识</td><td>REC_20250531_001</td></tr>
<tr><td>Timestamp</td><td>DateTime</td><td>识别发生时间</td><td>2025-05-31T10:30:00Z</td></tr>
<tr><td>CameraName</td><td>String</td><td>执行识别的摄像机名称</td><td>主变区_1号主变北侧_球机</td></tr>
<tr><td>PresetCode</td><td>String</td><td>预置位编号</td><td>P001</td></tr>
<tr><td>TargetEquipCode</td><td>String</td><td>目标设备运行编号 (若适用)</td><td>1B</td></tr>
<tr><td>RecognitionType</td><td>String</td><td>识别类型 (如: SwitchStatus, MeterReading, DefectDetection)</td><td>SwitchStatus</td></tr>
<tr><td>RecognitionItem</td><td>String</td><td>具体识别项 (如: Breaker_211_Status, OilLevel_1B)</td><td>Breaker_211_Status</td></tr>
<tr><td>RecognitionValue</td><td>String/JSON</td><td>识别值 (如: "合", "1", {"value": 25.5, "unit": "MPa"})</td><td>"1"</td></tr>
<tr><td>Confidence</td><td>Float</td><td>置信度 (0.0-1.0)</td><td>0.95</td></tr>
<tr><td>ImageURL</td><td>String</td><td>关联图像存储路径</td><td>/img/rec/20250531/img001.jpg</td></tr>
<tr><td>CheckValue (for E.6)</td><td>String</td><td>校核值 (0-分,1-合,2-接地,3-未知,4-异常)</td><td>"1"</td></tr>
</tbody>
</table>
<p>注：对于巡检结果中的识别结果，其格式遵循“数据规范与接口”E.5.3.5表E.39中`result`字段的定义：“识别项名称:识别结果”，单张照片多个结果以逗号分隔，多张照片结果以分号分隔。</p>
</li>
<li><strong>图像智能校核接口数据结构：</strong> 严格遵循“数据规范与接口”E.6.1.1表E.42、图E.53 (请求) 和 E.6.1.2表E.44、图E.56 (结果)。</li>
</ul>
<h4>3.2.5 关键算法/逻辑说明</h4>
<ul>
<li><strong>开关状态识别：</strong> 通常基于深度学习的图像分类或目标检测模型。训练模型时，需要大量包含开关各种状态（分、合、接地刀闸状态）以及不同光照、角度、遮挡情况下的图像样本。推理时，输入图像，模型输出开关的状态类别及置信度。</li>
<li><strong>仪表读数识别：</strong>
<ul>
<li>指针式仪表：首先定位仪表盘区域，然后通过霍夫变换等方法检测指针，识别刻度线和数字，最后根据指针与刻度的相对位置计算读数。可能需要图像校正和透视变换。</li>
<li>数字式仪表：定位数字显示区域，然后使用OCR技术识别数字。</li>
</ul>
</li>
<li><strong>缺陷检测（如渗油）：</strong> 可采用图像分割（如U-Net）或目标检测（如YOLO, Faster R-CNN）模型，训练模型自动识别图像中的油渍区域或缺陷特征。</li>
<li><strong>模型训练与更新：</strong> 智能识别模型的效果高度依赖训练数据的质量和数量。系统应考虑模型的持续优化和迭代更新机制。新采集的典型样本可用于模型的增量训练或重新训练。</li>
</ul>
<h4>3.2.6 异常处理机制</h4>
<ul>
<li><strong>识别置信度低：</strong> 当识别结果的置信度低于预设阈值时，系统可以标记为“不确定”或“需人工复核”，避免误报。</li>
<li><strong>图像质量差：</strong> 若输入图像模糊、过曝、欠曝或有严重遮挡，导致无法有效识别，系统应记录异常并提示检查摄像机状态或调整参数。</li>
<li><strong>算法模型错误：</strong> 算法本身可能存在缺陷或未覆盖某些特定场景，导致识别错误。应提供反馈机制，收集错误案例用于模型改进。</li>
<li><strong>E.6接口通信异常：</strong> 请求或接收校核结果时，若HTTPS通信失败，应按标准HTTP错误处理，主站或辅助系统可根据情况发起重试。</li>
</ul>
<h3><a id="toc-3-3"></a>3.3 安全防范及门禁子系统</h3>
<p>本子系统负责变电所物理安全防范，包括周界入侵检测、重点区域防护以及门禁系统的管理与控制。设计参考“现有设计文档”10.6.2安全防范子系统。</p>
<h4>3.3.1 功能描述</h4>
<ul>
<li><strong>周界入侵检测：</strong> 实时监测红外对射、激光对射、电子围栏等周界防护设备的状态，发生报警时立即上报并联动视频监控。</li>
<li><strong>区域入侵检测：</strong> 监测安装在重要区域（如主控室、高压室）的红外双鉴/三鉴探测器、玻璃破碎探测器的状态，实现对特定区域的布防和撤防控制。</li>
<li><strong>门禁状态监控与控制：</strong>
<ul>
<li>实时显示各门禁点的门磁状态（开/关）、锁状态（锁/开）。</li>
<li>记录人员刷卡、密码、指纹等方式的出入信息，包括时间、地点、人员身份。</li>
<li>支持远程控制门的开启（需相应授权和安全确认机制）。</li>
</ul>
</li>
<li><strong>报警联动：</strong>
<ul>
<li>入侵报警或门禁异常（如门被强开、门长时间未关）时，自动联动视频监控系统，弹出相关区域的实时视频画面并开始录像。</li>
<li>可联动现场声光报警器。</li>
<li>向指定用户发送报警通知（短信、App推送等）。</li>
</ul>
</li>
<li><strong>设备配置与管理：</strong>
<ul>
<li>安防设备（如红外对射、门禁控制器）的接入与参数配置。设备命名遵循“数据规范与接口”E.2.1（辅助监控设备配置表中的设备名称规则），并录入E.2.5.4（表E.8非视频设备配置表）。设备子类包括：门禁、红外双鉴、红外三鉴、红外对射、激光对射、玻璃破碎、电子围栏。</li>
</ul>
</li>
</ul>
<h4>3.3.2 处理流程 (示例：周界入侵报警)</h4>
<div class="figure-title">图3-3-1 周界入侵报警处理流程示意图</div>
<text c.="" mjuiihk9ijm0nsigzmlsbd0iizmzmyi+="" text="" x="MjUiIHk9IjMyNSIgZmlsbD0iIzMzMyI+  b. 联动视频 (弹窗/录像)&lt;/text&gt;&lt;text x=" 启动现场声光报警器<=""><text e.="" mjuiihk9ijm4nsigzmlsbd0iizmzmyi+="" text="" x="MjUiIHk9IjM2NSIgZmlsbD0iIzMzMyI+  d. (可选)短信/App通知&lt;/text&gt;&lt;text x=" 记录报警日志<=""><path d9im0xotugmtkwigwxmdagmcigc3ryb2tlpsijmzmziibtyxjrzxitzw5kpsj1cmwoi2fycm93agvhzdmpii8+phjly3qged0imjk1iib5psixnzaiihdpzhropsixodaiighlawdodd0indaiihj4psi1iibmawxspsijzmzlzwvliibzdhjva2u9iinjy2njy2milz48dgv4dcb4psizoduiihk9ije5nsigdgv4dc1hbmnob3i9im1pzgrszsi+报警信息上送主站<="" text=""><text alt="周界入侵报警处理流程示意图" x="Mzg1IiB5PSIyMTIiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZvbnQtc2l6ZT0iMTAiPi(通过E.3.2 IEC104)&lt;/text&gt;&lt;path d9Im05NSAzOTAgbjAgMjAiIG1hcmtlci1lbmQ9InVybCgjYXJyb3doZWFkMykiIHN0cm9rZT0iIzMzMyIvPjxyZWN0IHg9IjEwIiB5PSI0NDAiIHdpZHRoPSIxODAiIGhlaWdodD0iNDAiIHJ4PSI1IiBmaWxsPSIjZmZlZWVlIiBzdHJva2U9IiNjY2NjY2MiLz48dGV4dCB4PSI5NSIgeT0iNDY1IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj41. 等待人工确认与处理&lt;/text&gt;&lt;/g&gt;&lt;/svg&gt;"></text>
<ol>
<li><strong>周界探测器触发：</strong> 安装在变电所周界的红外对射、电子围栏等设备因非法入侵（如人员翻越、物体遮挡）而触发。</li>
<li><strong>辅助系统采集遥信变位：</strong> 探测器状态变化（如从正常变为报警）通过硬线或总线方式送至数据采集单元（如通信管理机或PLC），辅助监控系统的数据服务模块采集到此遥信变位信号。该遥信点在系统中应有明确定义，地址编码遵循“数据规范与接口”E.2.5.6中遥信地址编码规则（起始3001）。</li>
<li><strong>生成报警事件：</strong> 系统判断该遥信变位为有效报警后，在报警管理模块中生成一条报警事件，记录报警时间、报警设备名称、报警类型（如“周界入侵”）、报警级别等。</li>
<li><strong>执行报警联动：</strong>
<ol type="a">
<li><strong>本地告警：</strong> 在辅助监控系统客户端界面弹出报警窗口，播放报警声音，提示值班人员。</li>
<li><strong>视频联动：</strong> 自动调用与报警区域关联的摄像机（通常是周界监控摄像机），将其画面切换到大屏或弹出窗口，并启动录像。</li>
<li><strong>现场声光报警：</strong> （若配置）驱动现场的声光报警器发出警示。此操作可能涉及遥控点的输出，遥控地址遵循“数据规范与接口”E.2.5.5（起始24901）。</li>
<li><strong>通知发送：</strong> （若配置）通过短信网关、邮件服务器或App推送服务，将报警信息发送给预设的管理员或运维人员。</li>
<li><strong>日志记录：</strong> 将报警事件及所有联动操作记入系统日志。</li>
</ol>
</li>
<li><strong>报警信息上送主站：</strong> 辅助监控系统通过“数据规范与接口”E.3.2定义的数据采集接口（通常为IEC104规约），将此入侵报警信息（作为遥信）实时上送至段级/局级辅助监控主站。入侵报警属于重要报警，应确保及时上送。</li>
<li><strong>等待人工确认与处理：</strong> 值班人员确认报警，并根据实际情况进行处理（如派人查看、复位报警等）。处理过程和结果在系统中记录。</li>
</ol>
<h4>3.3.3 接口设计</h4>
<ul>
<li><strong>与主站的接口：</strong>
<ul>
<li>报警信息上送：通过“数据规范与接口”E.3.2的数据采集接口（IEC104规约），将安防报警（遥信）上送主站。</li>
<li>门禁远程控制：若主站需要远程控制门禁（如应急开门），则通过E.3.3的远方控制接口（IEC104规约）下发遥控命令。</li>
<li>基础数据：安防设备配置信息通过E.2.5.4（辅助监控设备配置表）的召唤接口提供给主站。相关遥信点、遥控点信息通过E.2.5.6（遥信点表）和E.2.5.5（遥控点表）的召唤接口提供。</li>
</ul>
</li>
<li><strong>与内部模块/设备的接口：</strong>
<ul>
<li>与安防设备（探测器、门禁控制器）：通常通过硬接点、RS485总线（如Modbus RTU）或TCP/IP（如厂家私有协议、Modbus TCP）连接至数据采集单元或直接接入网络。</li>
<li>与视频监控子系统：报警时，触发视频联动，向视频子系统发送指令，要求调用特定摄像机画面并启动录像。</li>
<li>与报警管理模块：上报探测到的原始报警信号。</li>
<li>与数据服务模块/后台服务：获取设备配置，上报状态和报警数据，存储门禁记录。</li>
</ul>
</li>
</ul>
<h4>3.3.4 数据结构与数据模型设计</h4>
<ul>
<li><strong>辅助监控设备配置表 (非视频设备 - 安防部分)：</strong> 基于“数据规范与接口”表E.8设计。关键字段：设备大类（安防）、设备子类（门禁、红外双鉴等）、设备名称（安装区域_位置描述_设备子类）。
                <p class="table-title">表3-3-1 非视频设备配置表 (安防设备示例 - E.8摘要)</p>
<table>
<thead><tr><th>字段名称</th><th>填写要求</th><th>样例</th></tr></thead>
<tbody>
<tr><td>所亭名称</td><td>必填</td><td>XX变电所</td></tr>
<tr><td>安装区域</td><td>必填，与E.2.3规范一致</td><td>周界围墙</td></tr>
<tr><td>位置描述</td><td>必填</td><td>东侧围墙中段</td></tr>
<tr><td>设备大类</td><td>必填，安防</td><td>安防</td></tr>
<tr><td>设备子类</td><td>必填，如红外对射</td><td>红外对射</td></tr>
<tr><td>设备名称</td><td>必填，自动生成</td><td>周界围墙_东侧围墙中段_红外对射</td></tr>
</tbody>
</table>
</li>
<li><strong>遥信点表 (安防报警信号)：</strong> 基于“数据规范与接口”表E.12设计。点位名称如“东门门磁状态”、“主控室红外报警”，状态描述如“开启/关闭”、“报警/正常”。遥信地址编码起始3001。</li>
<li><strong>遥控点表 (门禁控制、布防/撤防)：</strong> 基于“数据规范与接口”表E.10设计。点位名称如“东门开锁控制”、“主控室布防”，状态描述如“执行/复归”、“布防/撤防”。遥控地址编码起始24901。</li>
<li><strong>门禁记录数据模型：</strong>
<p class="table-title">表3-3-2 门禁记录数据模型 (示例)</p>
<table>
<thead><tr><th>字段名称</th><th>数据类型</th><th>说明</th><th>样例</th></tr></thead>
<tbody>
<tr><td>RecordID</td><td>String</td><td>记录唯一ID</td><td>ACC_20250531_001</td></tr>
<tr><td>Timestamp</td><td>DateTime</td><td>刷卡/事件时间</td><td>2025-05-31T09:00:00Z</td></tr>
<tr><td>DoorName</td><td>String</td><td>门禁点名称</td><td>主控室门</td></tr>
<tr><td>CardNumber</td><td>String</td><td>卡号 (若刷卡)</td><td>12345678</td></tr>
<tr><td>PersonID</td><td>String</td><td>人员ID (若关联)</td><td>EMP001</td></tr>
<tr><td>PersonName</td><td>String</td><td>人员姓名 (若关联)</td><td>张三</td></tr>
<tr><td>EventType</td><td>String</td><td>事件类型 (如: 正常进入, 尝试失败, 强开报警, 远程开门)</td><td>正常进入</td></tr>
<tr><td>Status</td><td>String</td><td>结果 (如: 成功, 失败)</td><td>成功</td></tr>
</tbody>
</table>
</li>
<li><strong>报警信息模型：</strong> 参考“现有设计文档”3.2.4，包含报警编号、设备编号、测点编号（关联的遥信点）、报警类型（如“门磁报警”、“入侵报警”）、报警等级、报警内容、报警时间等。</li>
</ul>
<h4>3.3.5 关键逻辑说明</h4>
<ul>
<li><strong>布防/撤防逻辑：</strong> 系统应支持对指定安防区域进行布防和撤防操作。布防状态下，相关探测器触发即产生报警；撤防状态下，探测器触发不产生报警（或只记录事件）。操作应有权限控制和日志记录。</li>
<li><strong>防拆报警逻辑：</strong> 许多安防探测器和设备本身具有防拆开关，当设备外壳被打开时会产生防拆报警。系统应能正确处理这类报警。</li>
<li><strong>联动规则配置：</strong> 报警联动规则（如哪个报警触发哪个摄像头、哪个声光报警器）应支持灵活配置，而不是硬编码。</li>
</ul>
<h4>3.3.6 异常处理机制</h4>
<ul>
<li><strong>探测器故障/离线：</strong> 系统应能监测安防探测器或控制器的在线状态和健康状况，发生故障或通信中断时产生设备故障报警。</li>
<li><strong>误报警处理：</strong> 对于易产生误报的探测器（如室外红外对射受恶劣天气影响），系统可设置报警延时、多次触发确认等机制以减少误报。运维人员也应能对误报警进行标记和分析。</li>
<li><strong>通信线路故障：</strong> 与安防设备（特别是采用总线方式的）通信线路故障时，应能定位故障范围并告警。</li>
<li><strong>门禁控制器失效：</strong> 若门禁控制器失效，应有应急处理预案，如手动开门机制，并确保记录。</li>
</ul>
<h3><a id="toc-3-4"></a>3.4 环境监测子系统</h3>
<p>环境监测子系统负责实时监测变电所内关键区域的环境参数，为设备安全运行提供环境保障，并及时预警潜在风险。设计参考“现有设计文档”10.6.1环境监测子系统。</p>
<h4>3.4.1 功能描述</h4>
<ul>
<li><strong>参数监测：</strong>
<ul>
<li><strong>温湿度监测：</strong> 监测室内外、开关柜室、控制室、电缆夹层等区域的空气温度和湿度。</li>
<li><strong>水浸监测：</strong> 在电缆沟、低洼区域等易积水位置部署水浸传感器，监测水位状态。</li>
<li><strong>SF6气体监测：</strong> 在GIS室等区域监测SF6气体浓度，及时发现泄漏。</li>
<li><strong>有害气体监测：</strong> (可选) 根据需要监测如O3、H2S等其他有害气体浓度。</li>
<li><strong>微气象信息监测：</strong> (可选，通常室外) 监测风速、风向、大气压力、降雨量等。</li>
</ul>
</li>
<li><strong>数据展示与分析：</strong>
<ul>
<li>实时数值显示各监测点的当前参数值。</li>
<li>历史数据查询与趋势曲线展示（日、周、月等）。</li>
<li>参数超限报警：可设置各参数的上上限、上限、下限、下下限报警阈值。</li>
</ul>
</li>
<li><strong>报警与联动：</strong>
<ul>
<li>环境参数超限（如温度过高、湿度过大、SF6泄漏、水浸等）时，自动产生声光报警。</li>
<li>联动控制：例如，温度过高时联动启动空调或风机；水浸报警时联动启动排水泵（若有）。</li>
<li>报警信息上送主站。</li>
</ul>
</li>
<li><strong>设备配置与管理：</strong>
<ul>
<li>环境监测传感器（如温湿度传感器、水浸传感器、SF6监测传感器、微气象站等）的接入与参数配置。设备命名遵循“数据规范与接口”E.2.1，并录入E.2.5.4（表E.8非视频设备配置表）。设备子类包括：水浸传感器、温湿度传感器、微气象站、SF6监测传感器。</li>
</ul>
</li>
</ul>
<h4>3.4.2 处理流程 (示例：温湿度超限报警)</h4>
<div class="figure-title">图3-4-1 温湿度超限报警处理流程示意图</div>
<path d9im05nsayotagbjagmjaiig1hcmtlci1lbmq9invybcgjyxjyb3dozwfknckiihn0cm9rzt0iizmzmyivpjxyzwn0ihg9ii0xmciget0imzmwiib3awr0ad0imjewiibozwlnahq9ijezmcigcng9ijuiigzpbgw9iinkywzmzweiihn0cm9rzt0iiznhytazzsivpjx0zxh0ihg9ijewmciget0imzuwiib0zxh0lwfuy2hvcj0ibwlkzgxlij41.="" text="" 执行报警联动：<=""><text (可选)联动空调="" b.="" mtuiihk9ijm5nsigzmlsbd0iizmzmyi+="" text="" x="MTUiIHk9IjM3NSIgZmlsbD0iIzMzMyI+  a. 界面/声音告警&lt;/text&gt;&lt;text x=" 风机<=""><text d.="" mtuiihk9ijqznsigzmlsbd0iizmzmyi+="" text="" x="MTUiIHk9IjQxNSIgZmlsbD0iIzMzMyI+  c. (可选)短信/App通知&lt;/text&gt;&lt;text x=" 记录报警日志<=""><path d9im0ymdugmjc1igwxmdagmcigc3ryb2tlpsijmzmziibtyxjrzxitzw5kpsj1cmwoi2fycm93agvhzdqpii8+phjly3qged0imza1iib5psiyntuiihdpzhropsixodaiighlawdodd0indaiihj4psi1iibmawxspsijzmzlzwvliibzdhjva2u9iinjy2njy2milz48dgv4dcb4psizotuiihk9iji4mcigdgv4dc1hbmnob3i9im1pzgrszsi+报警信息上送主站<="" text=""><text alt="温湿度超限报警处理流程示意图" x="Mzk1IiB5PSIyOTciIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZvbnQtc2l6ZT0iMTAiPi(通过E.3.2 IEC104)&lt;/text&gt;&lt;path d9Im05NSA0NjAgdjIwIiBtYXJrZXItZW5kPSJ1cmwoI2Fycm93aGVhZDQpIiBzdHJva2U9IiMzMzMyIvPjxyZWN0IHg9Ii0xMCIgeT0iNTAwIiB3aWR0aD0iMjEwIiBoZWlnaHQ9IjQwIiByeD0iNSIgZmlsbD0iI2ZlZWVlZSIgc3Ryb2tlPSIjY2NjY2NjIi8+PHRleHQgeD0iOTUiIHk9IjUyNSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+Ni4 等待人工确认/参数恢复&lt;/text&gt;&lt;/g&gt;&lt;/svg&gt;"></text>
<ol>
<li><strong>传感器持续采集：</strong> 温湿度传感器按照设定的周期（如每分钟）测量所在区域的温度和湿度值。</li>
<li><strong>辅助系统采集遥测数据：</strong> 数据服务模块通过Modbus或其他协议从传感器或其连接的数据采集单元读取温度和湿度值（作为遥测数据）。这些遥测点在系统中应有明确定义，地址编码遵循“数据规范与接口”E.2.5.7中遥测地址编码规则（起始20001）。</li>
<li><strong>数据与阈值比较：</strong> 系统将采集到的温湿度值与预设的报警阈值（如温度上限、湿度上限）进行比较。</li>
<li><strong>判断是否超限：</strong>
<ul>
<li><strong>否：</strong> 当前温湿度在正常范围内，返回步骤2继续监测。</li>
<li><strong>是：</strong> 温湿度值超出正常范围。</li>
</ul>
</li>
<li><strong>生成报警事件：</strong> 在报警管理模块中生成一条报警事件，记录报警时间、监测点名称、参数类型（温度/湿度）、当前值、阈值、报警级别等。</li>
<li><strong>执行报警联动：</strong>
<ol type="a">
<li><strong>本地告警：</strong> 在辅助监控系统客户端界面弹出报警窗口，播放报警声音。</li>
<li><strong>联动控制（可选）：</strong> 例如，若温度过高，可自动下发遥控命令启动空调制冷或开启通风风机。遥控命令地址编码遵循E.2.5.5（起始24901）。</li>
<li><strong>通知发送（可选）：</strong> 将报警信息发送给相关管理人员。</li>
<li><strong>日志记录：</strong> 记录报警事件及联动操作。</li>
</ol>
</li>
<li><strong>报警信息上送主站：</strong> 辅助监控系统通过“数据规范与接口”E.3.2的数据采集接口（IEC104规约），将此环境参数超限报警（作为遥信，或遥测值本身加质量戳）实时上送至段级/局级辅助监控主站。SF6泄漏、火灾相关温升报警等重要报警必须上送局级。</li>
<li><strong>等待人工确认或参数恢复：</strong> 值班人员确认报警，并采取相应措施。若环境参数自动恢复正常，报警状态也应能自动解除（根据配置策略）。</li>
</ol>
<h4>3.4.3 接口设计</h4>
<ul>
<li><strong>与主站的接口：</strong>
<ul>
<li>环境监测数据（遥测）及报警信息（遥信）上送：通过“数据规范与接口”E.3.2的数据采集接口（IEC104规约）上送主站。</li>
<li>环境监测设备配置信息：通过E.2.5.4（辅助监控设备配置表）的召唤接口提供给主站。</li>
<li>相关遥测点表：通过E.2.5.7（遥测点表）的召唤接口提供。</li>
<li>若有联动控制设备（如风机、水泵），其遥控点表通过E.2.5.5，遥信点表通过E.2.5.6提供。</li>
</ul>
</li>
<li><strong>与环境监测设备的接口：</strong>
<ul>
<li>通常通过RS485总线（Modbus RTU）、模拟量输入模块、数字量输入模块或TCP/IP（Modbus TCP、厂家私有协议）连接至数据采集单元或直接接入网络。</li>
<li>数据服务模块负责协议解析和数据读取。</li>
</ul>
</li>
</ul>
<h4>3.4.4 数据结构与数据模型设计</h4>
<ul>
<li><strong>辅助监控设备配置表 (非视频设备 - 环境监测部分)：</strong> 基于“数据规范与接口”表E.8设计。设备大类（环境监测）、设备子类（水浸传感器、温湿度传感器、微气象站、SF6监测传感器）、设备名称（安装区域_位置描述_设备子类）。
                 <p class="table-title">表3-4-1 非视频设备配置表 (环境监测设备示例 - E.8摘要)</p>
<table>
<thead><tr><th>字段名称</th><th>填写要求</th><th>样例</th></tr></thead>
<tbody>
<tr><td>所亭名称</td><td>必填</td><td>XX变电所</td></tr>
<tr><td>安装区域</td><td>必填，与E.2.3规范一致</td><td>控制室</td></tr>
<tr><td>位置描述</td><td>必填</td><td>控制柜A旁</td></tr>
<tr><td>设备大类</td><td>必填，环境监测</td><td>环境监测</td></tr>
<tr><td>设备子类</td><td>必填，如温湿度传感器</td><td>温湿度传感器</td></tr>
<tr><td>设备名称</td><td>必填，自动生成</td><td>控制室_控制柜A旁_温湿度传感器</td></tr>
</tbody>
</table>
</li>
<li><strong>遥测点表 (环境参数)：</strong> 基于“数据规范与接口”表E.14设计。点位名称如“主控室温度”、“GIS室SF6浓度”，单位如“℃”、“%RH”、“ppm”。遥测地址编码起始20001。</li>
<li><strong>遥信点表 (水浸报警、气体泄漏报警等开关量状态)：</strong> 基于“数据规范与接口”表E.12设计。点位名称如“电缆沟水浸报警”，状态描述如“报警/正常”。遥信地址编码起始3001。</li>
<li><strong>遥测数据模型：</strong> 参考“现有设计文档”3.2.1，包含测点编号、设备编号、测点名称、测点值、数据质量、时间戳、单位、上/下限值、报警等级等。</li>
<li><strong>报警信息模型：</strong> 参考“现有设计文档”3.2.4，详细记录环境报警信息。</li>
</ul>
<h4>3.4.5 关键逻辑说明</h4>
<ul>
<li><strong>多级报警阈值：</strong> 对重要环境参数（如SF6浓度、关键区域温度），可设置多级报警阈值（如预警、高报、高高报），对应不同的报警级别和处理预案。</li>
<li><strong>传感器漂移校准：</strong> 环境传感器长期运行可能出现精度漂移，系统应支持（或提示进行）定期校准，或与标准表进行比对修正。</li>
<li><strong>数据有效性判断：</strong> 对采集到的传感器数据进行有效性检查，如值是否在合理量程范围内，是否存在突变等，对无效数据进行标记。</li>
</ul>
<h4>3.4.6 异常处理机制</h4>
<ul>
<li><strong>传感器故障/离线：</strong> 系统应能监测传感器或其数据采集模块的在线状态，发生故障或通信中断时产生设备故障报警，并提示检查。</li>
<li><strong>通信异常：</strong> 与传感器通信失败时，应标记对应遥测点数据质量为“无效”，并记录通信错误。</li>
<li><strong>数据突变或超出合理范围：</strong> 若传感器数据发生不合逻辑的剧烈变化或远超正常量程，应重点关注，可能是传感器故障或真实极端情况。</li>
</ul>
<div class="key-points">
<h4>后续模块设计说明</h4>
<p>由于篇幅限制，后续的火灾报警子系统、动力照明控制子系统、红外测温模块、数据服务模块、系统管理与配置模块的设计将遵循与上述模块类似的结构和深度进行阐述，即：</p>
<ol>
<li><strong>功能描述：</strong> 详细说明模块的核心功能，并结合“数据规范与接口”中相关设备的配置要求。</li>
<li><strong>处理流程：</strong> 对关键业务流程（如火灾报警处理、远程灯控、红外测温任务）进行图文描述。</li>
<li><strong>接口设计：</strong> 明确与主站的HTTPS/IEC104接口（严格遵循“数据规范与接口”的定义），以及与内部模块/设备的接口。</li>
<li><strong>数据结构与数据模型设计：</strong> 详细说明涉及的设备配置表、点表（遥信、遥测、遥控、遥调）、以及核心业务数据模型，确保字段与“数据规范与接口”一致。</li>
<li><strong>关键算法/逻辑说明：</strong> 如火灾判断逻辑、节能控制策略、温度校正算法等。</li>
<li><strong>异常处理机制：</strong> 针对模块可能出现的异常情况（设备故障、通信中断、数据错误等）提出处理方案。</li>
</ol>
<p>在这些模块的设计中，将特别强调：</p>
<ul>
<li><strong>火灾报警子系统：</strong> 烟感、温感、手动报警按钮的接入，火灾报警信息的上送（E.3.2，重要报警），消防设备的联动控制（E.3.3）。</li>
<li><strong>动力照明控制子系统：</strong> 照明、风机、水泵、空调等设备的状态监测（遥信、遥测）和远程控制（遥控、遥调），相关点表（E.2.5.5-E.2.5.8）的规范应用。</li>
<li><strong>红外测温模块：</strong> 红外测温数据的采集（作为遥测），测温结果在巡检结果中的体现（E.5.3.5，表E.39 的 `result` 字段包含红外测温结果）。</li>
<li><strong>数据服务模块：</strong> 作为数据枢纽，严格执行“数据规范与接口”中E.2.5定义的各类基础数据点表的召唤接口实现，以及E.3.2数据采集与E.3.3远方控制的IEC104规约通信。</li>
<li><strong>系统管理与配置模块：</strong> 权限管理中需考虑与E.7摄像机鉴权机制的集成。</li>
</ul>
<p><strong>所有后续模块的设计均以“现有设计文档”为蓝本，并以“数据规范与接口”为准绳进行深化和细化。</strong></p>
</div>
<h2><a id="toc-4"></a>4. 接口设计详述</h2>
<p>本章节集中、详细地阐述变电所辅助监控系统与外部系统，特别是与辅助监控系统主站（段级、局级）的接口规范。接口设计严格遵循“数据规范与接口”文档，旨在确保数据交换的标准化、可靠性与互操作性。</p>
<h3><a id="toc-4-1"></a>4.1 接口设计总览</h3>
<p>系统对外接口主要分为两大类：</p>
<ol>
<li><strong>与辅助监控系统主站的接口：</strong> 这是最核心的对外接口，用于数据上报、指令接收、配置同步、巡检交互等。主要采用HTTPS承载JSON格式的API接口，以及基于TCP/IP的IEC 60870-5-104（104规约）通信。所有接口定义和数据结构均以“数据规范与接口”文档 (尤其是E.2至E.8节) 为唯一标准。</li>
<li><strong>与所内其他系统/设备的接口：</strong> 包括与NVR、各类传感器、控制器等的接口，这些多为设备厂家SDK、标准工业协议（如Modbus、ONVIF）或硬接点。</li>
</ol>
<p>接口设计遵循以下原则：</p>
<ul>
<li><strong>标准化：</strong> 优先采用国家标准、行业标准或国际通用标准。</li>
<li><strong>安全性：</strong> HTTPS接口强制使用TLS/SSL加密，API调用需进行身份认证（如Token）。</li>
<li><strong>可靠性：</strong> 对于关键数据交互，应设计重传机制和确认机制。</li>
<li><strong>易用性：</strong> API接口设计力求简洁明了，参数定义清晰。</li>
<li><strong>可扩展性：</strong> 接口设计应考虑未来功能扩展的需求。</li>
</ul>
<h3><a id="toc-4-2"></a>4.2 与辅助监控系统主站接口</h3>
<p>这部分接口是系统与上级管理平台进行信息交互的桥梁，其设计细节直接影响整个监控体系的效能。</p>
<h4>4.2.1 基础数据互提接口 (HTTPS API)</h4>
<p>辅助监控系统厂商向主站厂商提供基础数据时，采用Excel表格形式，但当工程实施完成后，若基础数据更新，段级或局级辅助监控区可通过辅助监控系统提供的HTTPS接口召唤点表。所有此类接口均由辅助监控系统提供，采用HTTPS协议，POST方式提交，数据交换格式为JSON字符串，header需包含token字符串。详细定义见“数据规范与接口”E.2.5。</p>
<ul>
<li><strong>召唤辅助监控设备配置表接口 (E.2.5.4)：</strong>
<ul>
<li>用途：主站召唤所内视频和非视频设备配置信息。</li>
<li>路径：`https://ip:port/项目路径/api/callDeviceList`</li>
<li>返回数据结构：参考“数据规范与接口”图E.6及表E.9（实际应为表E.7和表E.8的集合）。</li>
</ul>
</li>
<li><strong>召唤辅助监控遥控点表接口 (E.2.5.5)：</strong>
<ul>
<li>用途：主站召唤所内所有可遥控的辅助设备点位信息。</li>
<li>路径：`https://ip:port/项目路径/api/callRemoteControlList`</li>
<li>返回数据结构：参考“数据规范与接口”图E.9及表E.11（实际内容依据表E.10）。</li>
</ul>
</li>
<li><strong>召唤辅助监控遥信点表接口 (E.2.5.6)：</strong>
<ul>
<li>用途：主站召唤所内所有遥信量点位信息。</li>
<li>路径：`https://ip:port/项目路径/api/callRemoteSignalList`</li>
<li>返回数据结构：参考“数据规范与接口”图E.12及表E.13（实际内容依据表E.12）。</li>
</ul>
</li>
<li><strong>召唤辅助监控遥测点表接口 (E.2.5.7)：</strong>
<ul>
<li>用途：主站召唤所内所有遥测量点位信息。</li>
<li>路径：`https://ip:port/项目路径/api/callRemoteMeterList`</li>
<li>返回数据结构：参考“数据规范与接口”图E.15及表E.15（实际内容依据表E.14）。</li>
</ul>
</li>
<li><strong>召唤辅助监控遥调点表接口 (E.2.5.8)：</strong>
<ul>
<li>用途：主站召唤所内所有可遥调的辅助设备点位信息。</li>
<li>路径：`https://ip:port/项目路径/api/callRemoteAdjustList`</li>
<li>返回数据结构：参考“数据规范与接口”图E.18及表E.17（实际内容依据表E.16）。</li>
</ul>
</li>
<li><strong>召唤摄像机预置位点表接口 (E.2.5.9)：</strong>
<ul>
<li>用途：主站召唤所有摄像机（含固定和导轨巡检）的预置位信息。</li>
<li>路径：`https://ip:port/项目路径/api/callCameraPresetList`</li>
<li>返回数据结构：参考“数据规范与接口”图E.21及表E.19（实际内容依据表E.18）。</li>
</ul>
</li>
</ul>
<p class="note">对于上述各召唤接口，返回数据结构中的Key1、Key2、值1、值2仅为示例，实际返回的JSON应为包含符合对应点表规范（如表E.7, E.8, E.10, E.12, E.14, E.16, E.18）所有字段的对象数组。</p>
<h4>4.2.2 数据采集及远方控制接口 (IEC104规约)</h4>
<p>此类接口主要用于实时数据的上报和控制命令的下发，采用Q/CR 796-2020附录I的104传输规约。详见“数据规范与接口”E.3。</p>
<ul>
<li><strong>数据采集接口 (E.3.2)：</strong>
<ul>
<li>数据方向：辅助监控系统 → 段级/局级辅助监控区。</li>
<li>接口提供方：段级/局级辅助监控区（即主站作为104主站，辅助系统作为104子站）。</li>
<li>内容：辅助监控系统将所内安防、环境监控、消防等辅助设备的设备状态（遥信，含报警）和测量值（遥测，含遥调档位）实时上送。全部报警信息上送段级，重要报警（如火灾、SF6泄漏）上送局级。</li>
<li>接口图：参考“数据规范与接口”图E.22（数据采集）和图E.23（上送报警信息）。</li>
</ul>
</li>
<li><strong>远方控制接口 (E.3.3)：</strong>
<ul>
<li>数据方向：段级辅助监控区 → 辅助监控系统。</li>
<li>接口提供方：辅助监控系统（即辅助系统作为104子站，接收主站控制命令）。</li>
<li>内容：段级主站按需对辅助系统的照明、风机、水泵、空调、门禁等辅助监控设备进行远方控制（遥控和遥调）。</li>
<li>接口图：参考“数据规范与接口”图E.24。</li>
</ul>
</li>
</ul>
<p>IEC104协议的具体应用（如信息对象地址、ASDU类型等）需遵循Q/CR 796-2020和“现有设计文档”附录A.2.1的规范。</p>
<h4>4.2.3 视频调阅及控制接口</h4>
<p>此类接口用于主站调阅辅助监控系统的实时视频、历史录像以及控制摄像机。详见“数据规范与接口”E.4。</p>
<ul>
<li><strong>调阅视频接口 (E.4.2)：</strong>
<ul>
<li>范围：段级、局级主站均应实现。</li>
<li>实现方式：所有摄像机（含固定和移动巡检）接入所内NVR，NVR接入段级/局级主站。主站通过NVR获取实时视频（主子码流切换，子码流不高于720P，主码流不低于720P）和录像。</li>
<li>预置位调用：固定摄像机预置位通过NVR调用；移动巡检设备预置位通过下述HTTPS接口调用。</li>
<li>接口标准：GB/T 28181 或 ONVIF协议。</li>
</ul>
</li>
<li><strong>调用移动巡检设备预置位的接口 (HTTPS API, E.4.3)：</strong>
<ul>
<li>数据方向：铁路供电调度控制系统主站 → 辅助监控系统。</li>
<li>接口提供方：辅助监控系统。</li>
<li>路径：`https://ip:port/项目路径/api/callPreset`</li>
<li>请求数据结构 (表E.20)：`{ "subName": "XX变电所", "camName": "高压室_顶部_导轨", "presetCode": "2" }`</li>
<li>返回数据结构 (表E.21)：`{ "status": 0, "message": "调用成功" }`</li>
<li>接口图：参考“数据规范与接口”图E.25。</li>
</ul>
</li>
<li><strong>获取移动巡检设备当前位置的接口 (HTTPS API, E.4.4)：</strong>
<ul>
<li>数据方向：铁路供电调度控制系统主站 → 辅助监控系统。</li>
<li>接口提供方：辅助监控系统。</li>
<li>路径：`https://ip:port/项目路径/api/getCurrentPosition`</li>
<li>请求数据结构 (表E.22)：`{ "subName": "XX变电所", "camName": "高压室_顶部_导轨" }`</li>
<li>返回数据结构 (表E.23, 表E.24)：`{ "status": 0, "message": "获取成功", "data": { "x": 33.30, "y": 23.31, "z": 4.81, "time": "2019-01-01 12:02:35" } }`</li>
<li>坐标说明：设备坐标原点为设备自身坐标系，三维直角，X、Y水平，Z垂直向上，单位米，精度小数点后两位。</li>
<li>接口图：参考“数据规范与接口”图E.28。</li>
</ul>
</li>
<li><strong>设置摄像机的预置位接口 (HTTPS API, E.4.5)：</strong>
<ul>
<li>数据方向：辅助监控系统段级主站 → 辅助监控系统。</li>
<li>接口提供方：辅助监控系统。</li>
<li>用途：主站支持设置摄像机预置位（修改、新增），通过此接口通知辅助系统保存。</li>
<li>路径：`https://ip:port/项目路径/api/savePreset`</li>
<li>请求数据结构 (表E.25)：`{ "subName": "XX变电所", "equipName": "1021_隔离开关", "camName": "进线区_东侧龙门柱_球机", "presetName": "1021-B相出线线夹", "presetCode": "23" }`</li>
<li>返回数据结构 (表E.26)：`{ "status": 0, "message": "保存成功" }`</li>
<li>接口图：参考“数据规范与接口”图E.31。</li>
</ul>
</li>
</ul>
<h4>4.2.4 视频巡检接口 (HTTPS API)</h4>
<p>此类接口规范了主站与辅助监控系统之间关于视频巡检任务的全流程交互。范围为段级主站与辅助系统间。详见“数据规范与接口”E.5。</p>
<p>整体交互流程参考“数据规范与接口”图E.34。</p>
<ul>
<li><strong>下发巡检计划接口 (E.5.3.1)：</strong>
<ul>
<li>数据方向：段级辅助监控系统主站 → 辅助监控系统。</li>
<li>接口提供方：辅助监控系统。</li>
<li>路径：`https://ip:port/项目路径/api/downloadPlan`</li>
<li>请求数据结构 (图E.36, 表E.27-E.29)：包含巡检计划信息（名称、编号、所亭、主站类型、停留时间）、周期信息（周几、启动日期、启动时间）、巡检点位信息（序号、摄像机名、预置位号、拍照张数、停留时间）。点位信息为空表示删除计划。</li>
<li>返回数据结构 (图E.37, 表E.30)：`{ "status": 0, "message": "下发成功" }`</li>
</ul>
</li>
<li><strong>下发临时巡检任务接口 (E.5.3.2)：</strong>
<ul>
<li>数据方向：局级/段级主站 → 辅助监控系统。</li>
<li>接口提供方：辅助监控系统。</li>
<li>路径：`https://ip:port/项目路径/api/downloadTempTask`</li>
<li>请求数据结构 (图E.39, 表E.31-E.32)：包含巡检任务信息（同计划）、巡检点位信息（同计划）。</li>
<li>返回数据结构 (图E.40, 表E.33)：`{ "status": 0, "message": "下发成功" }`</li>
</ul>
</li>
<li><strong>巡检控制接口 (E.5.3.3)：</strong>
<ul>
<li>数据方向：段级辅助监控区 → 辅助监控系统。</li>
<li>接口提供方：辅助监控系统。</li>
<li>路径：`https://ip:port/项目路径/api/controlTask`</li>
<li>请求数据结构 (图E.42, 表E.34)：`{ "name": "日常巡检", "code": "TASK001", "opt": "start" }` (opt: start, pause, continue, end)</li>
<li>返回数据结构 (图E.43, 表E.35)：`{ "status": 0, "message": "控制成功" }`</li>
</ul>
</li>
<li><strong>上传巡检进度接口 (E.5.3.4)：</strong>
<ul>
<li>数据方向：辅助监控系统 → 局级/段级辅助监控区。</li>
<li>接口提供方：局级/段级辅助监控区。</li>
<li>路径：`https://ip:port/项目路径/api/uploadTaskProgress`</li>
<li>请求数据结构 (图E.45, 表E.36)：包含任务编号、巡检序号、所亭名称、摄像机名称、预置位编号。</li>
<li>返回数据结构 (图E.46, 表E.37)：`{ "status": 0, "message": "上传成功" }`</li>
</ul>
</li>
<li><strong>上传巡检结果接口 (E.5.3.5)：</strong>
<ul>
<li>数据方向：辅助监控系统 → 局级/段级辅助监控区。</li>
<li>接口提供方：局级/段级辅助监控区。</li>
<li>路径：`https://ip:port/项目路径/api/uploadTaskResult`</li>
<li>数据格式：文件使用 `multipart/form-data` 上传，header含token。大数据量分批上传（每包不超256MB，中间包endTime为空，最后包有值）。</li>
<li>请求数据结构 (图E.48, 表E.38-E.39)：包含巡检计划信息（名称、编号、所亭、开始/结束时间）、巡检点位信息（序号、摄像机名、预置位号、照片数量、拍照时间、照片文件pics[]、识别结果result、执行状态status、描述message）。</li>
<li>返回数据结构 (图E.49, 表E.40)：`{ "status": 0, "message": "上传成功" }`</li>
</ul>
</li>
<li><strong>召唤巡检结果接口 (E.5.3.6)：</strong>
<ul>
<li>数据方向：局级/段级辅助监控区 → 辅助监控系统。</li>
<li>接口提供方：辅助监控系统。</li>
<li>用途：网络中断恢复后，主站通知辅助系统上送未上传的巡检结果（按E.5.3.5接口）。</li>
<li>路径：`https://ip:port/项目路径/api/callTaskResult`</li>
<li>请求数据：无。</li>
<li>返回数据结构 (图E.51, 表E.41)：`{ "status": 0, "message": "召唤成功，准备上传" }`</li>
</ul>
</li>
</ul>
<h4>4.2.5 图像智能校核接口 (HTTPS API)</h4>
<p>用于主站在开关变位后请求辅助系统通过图像识别进行状态校核。详见“数据规范与接口”E.6。</p>
<ul>
<li><strong>请求开关变位智能校核 (E.6.1.1)：</strong>
<ul>
<li>数据方向：辅助监控系统主站 → 辅助监控系统。</li>
<li>接口提供方：辅助监控系统。</li>
<li>路径：`https://ip:port/项目路径/api/pscada/checkRequest`</li>
<li>请求数据结构 (图E.53, 表E.42)：`{ "subName": "XX变电所", "equipCode": "211", "camName": "进线区_东侧龙门柱_球机", "presetCode": "2", "soeTime": 1619334895970 }`</li>
<li>返回数据结构 (图E.54, 表E.43)：`{ "status": 0, "message": "请求成功" }`</li>
</ul>
</li>
<li><strong>接收开关变位智能校核结果 (E.6.1.2)：</strong>
<ul>
<li>数据方向：辅助监控系统 → 辅助监控系统主站。</li>
<li>接口提供方：辅助监控系统主站。</li>
<li>路径：`https://ip:port/项目路径/api/pscada/checkResult`</li>
<li>请求数据结构 (图E.56, 表E.44)：`{ "subName": "XX变电所", "equipCode": "211", ..., "checkValue": "1" }` (checkValue: 0分, 1合, 2接地, 3未知, 4异常)</li>
<li>返回数据结构 (图E.57, 表E.45)：`{ "status": 0, "message": "接收成功" }`</li>
</ul>
</li>
</ul>
<h4>4.2.6 摄像机鉴权接口 (HTTPS API)</h4>
<p>规范了主站和辅助系统在控制摄像机前与鉴权中心的交互流程。详见“数据规范与接口”E.7。</p>
<p>摄像机控制权限优先级定义见“数据规范与接口”表E.46，从高到低共12级，如第一级所内火灾报警联动 (HZBJ)，第十二级所内视频巡检 (SJXJ)。</p>
<ul>
<li><strong>申请摄像机控制权 (E.7.1.2)：</strong>
<ul>
<li>数据方向：辅助监控主站/辅助监控系统 → 鉴权中心。</li>
<li>接口提供方：鉴权中心。</li>
<li>路径：`https://ip:port/项目路径/api/cameraAuth/apply`</li>
<li>请求数据结构 (图E.59, 表E.47)：`{ "subName": "XX变电所", "camNames": ["进线区_东侧龙门柱_球机"], "code": "HZBJ" }`</li>
<li>返回数据结构 (图E.60, 表E.48)：包含status, message, 及data对象（内含成功/失败列表，失败时含ttl和desc）。</li>
</ul>
</li>
<li><strong>释放摄像机控制权 (E.7.1.3)：</strong>
<ul>
<li>数据方向：辅助监控主站/辅助监控系统 → 鉴权中心。</li>
<li>接口提供方：鉴权中心。</li>
<li>路径：`https://ip:port/项目路径/api/cameraAuth/free`</li>
<li>请求数据结构 (图E.62, 表E.49)：同申请接口。</li>
<li>返回数据结构 (图E.63, 表E.50)：`{ "status": 0, "message": "释放成功" }`</li>
</ul>
</li>
<li><strong>摄像机控制权变更通知 (E.7.1.4)：</strong>
<ul>
<li>数据方向：鉴权中心 → 辅助监控系统。</li>
<li>接口提供方：辅助监控系统。</li>
<li>路径：`https://ip:port/项目路径/api/cameraAuth/notice`</li>
<li>请求数据结构 (图E.65, 表E.51)：`{ "subName": "XX变电所", "idleCamNames": [], "occupyCamNames": ["..."] }`</li>
<li>返回数据结构 (图E.66, 表E.52)：`{ "status": 0, "message": "通知已接收" }`</li>
</ul>
</li>
</ul>
<h4>4.2.7 时钟同步接口 (NTP或104规约)</h4>
<p>辅助监控系统接收铁路供电调度控制系统主站的时钟同步信息，确保全系统时间一致性。详见“数据规范与接口”E.8。</p>
<ul>
<li>采用NTP协议：辅助监控系统作为NTP客户端，向主站指定的NTP服务器同步时间。</li>
<li>采用DL/T 634.5104通信协议：通过104规约的时钟同步命令（C_CS_NA_1，类型标识103）进行同步。</li>
</ul>
<h3><a id="toc-4-3"></a>4.3 与所内其他系统/设备接口</h3>
<p>参考“现有设计文档”4.4节及各模块设计。</p>
<ul>
<li><strong>与NVR/摄像机：</strong>
<ul>
<li>视频流获取：RTSP协议。</li>
<li>设备控制与状态获取：ONVIF协议、GB/T 28181或厂家私有SDK (如海康、大华)。控制命令包括PTZ、预置位调用/设置、抓图、录像启停等。</li>
</ul>
</li>
<li><strong>与各类传感器/控制器（安防、环境、消防、动力）：</strong>
<ul>
<li>通常通过RS485 (Modbus RTU)、以太网 (Modbus TCP、BACnet/IP、SNMP)、硬接点（DI/DO）、模拟量输入（AI）等方式连接到通信管理机或直接接入辅助监控系统的采集模块。</li>
<li>数据服务模块负责对应协议的解析和数据采集/控制命令下发。</li>
</ul>
</li>
<li><strong>（可选）与综合自动化系统（SAS）：</strong> 若需要与SAS进行数据交互（如共享部分设备状态，或接收SAS的联动指令），可能通过OPC、Modbus或其他约定协议。本方案主要侧重辅助监控系统自身及其与主站的接口。</li>
</ul>
<h3><a id="toc-4-4"></a>4.4 数据交换格式与协议总结</h3>
<p>参考“现有设计文档”4.1节和附录A.2.2。</p>
<ul>
<li><strong>主要数据交换格式：</strong>
<ul>
<li><strong>JSON (JavaScript Object Notation)：</strong> 作为HTTPS API接口请求和响应的主要数据格式，轻量、易读、易解析。</li>
<li><strong>二进制格式：</strong> IEC104规约本身采用特定的二进制帧格式。视频流（H.264/H.265）、图片（JPEG/PNG）也是二进制。</li>
</ul>
</li>
<li><strong>主要通信协议：</strong>
<ul>
<li><strong>HTTPS：</strong> 用于与主站之间的大部分API接口通信，保证传输安全。</li>
<li><strong>IEC 60870-5-104：</strong> 用于与主站之间的实时数据（遥信、遥测）上报和远方控制（遥控、遥调）命令下发。</li>
<li><strong>RTSP/RTMP：</strong> 用于视频流传输。</li>
<li><strong>ONVIF/GB/T 28181/厂家SDK：</strong> 用于与视频设备的交互。</li>
<li><strong>Modbus (TCP/RTU)：</strong> 常用于与工业现场设备（传感器、控制器）的通信。</li>
<li><strong>NTP：</strong> 用于时间同步。</li>
<li><strong>WebSocket：</strong> (可选，参考“现有设计文档”A.2.2.2) 可用于客户端与后台服务间的实时数据推送。</li>
</ul>
</li>
</ul>
<div class="key-points">
<h4>关键要点总结：接口设计详述</h4>
<ul>
<li>与主站的接口是核心，严格遵循“数据规范与接口”文档，包含基础数据召唤、实时数据与控制（104规约）、视频调阅控制、视频巡检全流程、图像智能校核、摄像机鉴权等HTTPS API。</li>
<li>接口类型多样，包括HTTPS API (JSON)、IEC104、GB/T 28181、ONVIF、厂家SDK、Modbus等。</li>
<li>所有HTTPS API接口均采用POST方式（除特定说明），header需带token，请求/响应体为JSON。</li>
<li>详细的请求/响应数据结构字段直接来源于“数据规范与接口”中的表格定义。</li>
<li>时钟同步确保系统时间统一。</li>
</ul>
</div>
<h2><a id="toc-5"></a>5. 数据规范遵循说明</h2>
<p>本系统在设计和实现过程中，严格遵循“数据规范与接口”文档中关于数据定义、命名、格式等各项规范，以确保数据的一致性、准确性、规范性及互操作性。</p>
<h3><a id="toc-5-1"></a>5.1 供电设备命名规范应用</h3>
<p>参照“数据规范与接口”E.2.1及表E.1，系统在处理供电设备信息时，全面应用了该命名规范。</p>
<ul>
<li><strong>数据录入与存储：</strong> 在系统后台配置供电设备基础信息时，“线路名称”、“所亭名称”、“设备类型名称”、“运行编号”为必填字段。系统内部存储这些分立的字段。</li>
<li><strong>设备名称生成与显示：</strong> “设备名称”字段由系统根据规则“运行编号_设备类型名称”自动生成并存储。例如，如果运行编号为“1B”，设备类型名称为“牵引变压器”，则设备名称为“1B_牵引变压器”。在各类界面展示、报表输出及点表生成时均使用此规范名称。</li>
<li><strong>点表提供：</strong> 在通过E.2.5接口向主站提供各类基础数据点表（如辅助监控设备配置表、摄像机预置位点表关联的目标设备）时，凡涉及供电设备名称的字段，均按此规范填写或引用。</li>
<li><strong>设备类型列表：</strong> 设备类型名称的选取严格遵循E.2.1表E.1中列出的设备类型列表（牵引变压器、断路器等），其他未列出设备类型参考“铁路牵引供电设备设施单元划分、编码暂行规范中基本单元代码”。</li>
</ul>
<p><strong>示例：</strong> 对于京沪高铁XX变电所的1号断路器，其运行编号为101，则：</p>
<ul>
<li>线路名称：京沪高铁</li>
<li>所亭名称：XX变电所</li>
<li>设备类型名称：断路器</li>
<li>运行编号：101</li>
<li>设备名称（自动生成）：101_断路器</li>
</ul>
<h3><a id="toc-5-2"></a>5.2 摄像机图像显示规范实现</h3>
<p>参照“数据规范与接口”E.2.2及表E.2，系统在客户端的视频显示界面上，通过OSD（On-Screen Display）技术叠加显示规范要求的信息，以增强监控画面的信息含量和可追溯性。</p>
<ul>
<li><strong>当前时间：</strong>
<ul>
<li>位置：画面左上角。</li>
<li>格式：`YYYY-MM-DD HH:mm:ss` (例如：`2019-08-21 20:00:00`)。</li>
<li>实现：客户端获取系统当前时间或同步NVR时间，按指定格式叠加。</li>
</ul>
</li>
<li><strong>摄像机名称：</strong>
<ul>
<li>位置：画面右上角。</li>
<li>格式：参见摄像机预置位点表中的摄像机名称（即“辅助监控设备配置表”中的“设备名称”，例如：`进线区_东侧龙门柱_球机`）。</li>
<li>实现：客户端根据当前播放的视频通道，从设备配置中获取摄像机名称并叠加。</li>
</ul>
</li>
<li><strong>预置位名称：</strong>
<ul>
<li>位置：画面右下角。</li>
<li>格式：参见摄像机预置位点表中的预置位名称（例如：`101-A相进线线夹`）。若当前非预置位状态，则此区域可为空或显示默认信息。</li>
<li>实现：当摄像机调用到某个预置位时，客户端获取该预置位名称并叠加。</li>
</ul>
</li>
<li><strong>所亭名称：</strong>
<ul>
<li>位置：画面左下角。</li>
<li>格式：`XX变电所/AT所/分区所/配电所`（根据实际所亭类型选择）。</li>
<li>实现：客户端从系统配置中获取当前变电所的规范名称并叠加。</li>
</ul>
</li>
</ul>
<p>此功能由客户端软件实现，确保视频图像无论实时浏览还是录像回放（若录像时已叠加）均包含这些信息。</p>
<h3><a id="toc-5-3"></a>5.3 辅助监控设备安装区域命名规范应用</h3>
<p>参照“数据规范与接口”E.2.3及表E.3 (文档中原为F.2，应为E.3)，系统在配置和管理辅助监控设备时，严格使用规范中定义的安装区域名称。</p>
<p class="table-title">表5-1 辅助监控设备安装区域名称规范 (E.3)</p>
<table>
<thead><tr><th>序号</th><th>安装区域名称</th></tr></thead>
<tbody>
<tr><td>1</td><td>进线区</td></tr>
<tr><td>2</td><td>主变区</td></tr>
<tr><td>3</td><td>进线高压室</td></tr>
<tr><td>4</td><td>27.5kV高压室</td></tr>
<tr><td>5</td><td>馈线区</td></tr>
<tr><td>6</td><td>所用变室</td></tr>
<tr><td>7</td><td>控制室</td></tr>
<tr><td>8</td><td>电缆夹层</td></tr>
<tr><td>9</td><td>通信机械室</td></tr>
<tr><td>10</td><td>屋顶</td></tr>
<tr><td>11</td><td>周界围墙</td></tr>
<tr><td>12</td><td>其他</td></tr>
</tbody>
</table>
<ul>
<li><strong>设备配置：</strong> 在“辅助监控设备配置表”（“数据规范与接口”表E.7视频设备、表E.8非视频设备）中，“安装区域”字段的填写必须从上述规范列表中选择。</li>
<li><strong>设备命名：</strong> “辅助监控设备配置表”中的“设备名称”自动生成规则为“安装区域_位置描述_设备子类(+双光谱通道)”，确保了安装区域信息融入设备唯一标识。</li>
<li><strong>界面显示与查询：</strong> 系统在按区域展示设备、筛选设备或生成相关报表时，均使用这些规范的区域名称。</li>
</ul>
<h3><a id="toc-5-4"></a>5.4 摄像机拍摄焦点命名规范应用</h3>
<p>参照“数据规范与接口”E.2.4及表E.4，此规范是确保摄像机预置位能准确描述其监控目标的关键。系统在配置摄像机预置位时，预置位名称遵循“供电设备运行编号-拍摄焦点中文名称”的规则。</p>
<ul>
<li><strong>预置位配置界面：</strong> 系统在提供摄像机预置位配置功能时，会引导用户根据被摄供电设备类型，从表E.4中选择合适的“供电设备应关注的焦点类别”和具体的“拍摄焦点名称（举例）”。</li>
<li><strong>预置位名称生成：</strong> 用户输入或选择供电设备运行编号，并选择了拍摄焦点后，系统自动或辅助生成符合规范的预置位名称。</li>
<li><strong>应用举例 (来自表E.4)：</strong>
<ul>
<li>**断路器 (运行编号示例: 211)**
                        <ul>
<li>拍摄焦点: 全景 → 预置位名称: `211-全景`</li>
<li>拍摄焦点: 气体压力表计 → 预置位名称: `211-气体压力表计`</li>
</ul>
</li>
<li>**变压器 (运行编号示例: TR01)**
                        <ul>
<li>拍摄焦点: 油位计 → 预置位名称: `TR01-油位计`</li>
<li>拍摄焦点: A相进线连接点 → 预置位名称: `TR01-A相进线连接点`</li>
</ul>
</li>
</ul>
</li>
<li><strong>点表提供：</strong> 在“摄像机预置位点表”（“数据规范与接口”表E.18）中，“预置位名称”字段严格按照此规范填写。</li>
</ul>
<p>通过严格执行此规范，使得运维人员能够通过预置位名称快速理解其监控内容，也为主站系统解析和使用预置位信息提供了便利。</p>
<h3><a id="toc-5-5"></a>5.5 基础数据点表规范实现</h3>
<p>系统设计和实现严格遵循“数据规范与接口”E.2.5中定义的各类基础数据点表格式和填写要求（表E.5至E.18）。</p>
<ul>
<li><strong>牵引变电所基本情况表 (表E.5)：</strong> 系统存储和提供线路名称、所亭名称、辅助监控设备布置图文件名、所亭坐标、HTTP服务地址等信息。</li>
<li><strong>视频服务器配置信息表 (表E.6)：</strong> 管理NVR或视频服务器的IP地址、端口、用户名、密码等。</li>
<li><strong>辅助监控设备配置表 (表E.7视频设备, 表E.8非视频设备)：</strong> 这是设备管理的核心，严格按照字段要求（如安装区域、位置描述、设备大/子类、设备名称生成规则）进行配置和管理。</li>
<li><strong>辅助监控遥控点表 (表E.10)：</strong> 定义所有遥控点，包括设备名称、点位名称、遥控状态描述、遥控地址编码（十进制，起始24901，全所唯一，与综自区分）。</li>
<li><strong>辅助监控遥信点表 (表E.12)：</strong> 定义所有遥信点，包括设备名称、点位名称、遥信状态描述、遥信地址编码（十进制，起始3001，全所唯一，与综自区分）。</li>
<li><strong>辅助监控遥测点表 (表E.14)：</strong> 定义所有遥测点，包括设备名称、点位名称、单位、遥测地址编码（十进制，起始20001，全所唯一，与综自区分）。</li>
<li><strong>辅助监控遥调点表 (表E.16)：</strong> 定义所有遥调点，包括设备名称、点位名称、操作类型、状态描述（升降档必填）、遥调地址编码（十进制，起始25001，全所唯一，与综自区分）。</li>
<li><strong>摄像机预置位点表 (表E.18)：</strong> 详见5.1和5.4节，严格遵循预置位相关规范。</li>
</ul>
<p>系统的数据服务模块负责这些点表的生成、管理和通过E.2.5中定义的HTTPS API接口按需提供给主站。内部数据库表结构设计会充分考虑这些规范字段，确保数据的一致性和完整性。</p>
<h3><a id="toc-5-6"></a>5.6 数据字典与编码规范</h3>
<p>为确保系统内部及与外部交互数据的一致性和标准化，系统建立并应用统一的数据字典和编码规范。</p>
<ul>
<li><strong>设备类型编码：</strong> “供电设备命名规范”（E.2.1）中列出的设备类型（如牵引变压器、断路器等）及其对应的编码（参考“铁路牵引供电设备设施单元划分、编码暂行规范中基本单元代码”）在系统内部统一管理。</li>
<li><strong>辅助监控设备分类编码：</strong> “辅助监控设备配置表”（E.2.5.4）中定义的设备大类（视频、音频、安防、环境监测、消防、动力照明）和设备子类（枪机、门禁、水浸传感器等）均有内部统一编码。</li>
<li><strong>遥信/遥控状态编码：</strong> 单点对象状态0/1，双点对象状态1/2的含义在系统中统一解释和应用。</li>
<li><strong>摄像机控制权限编码 (E.7.1.1, 表E.46)：</strong> 如HZBJ（火灾报警）、GZTZ（故障跳闸）、YKCZ（遥控操作）等，在摄像机鉴权流程中严格使用。</li>
<li><strong>图像智能校核结果编码 (E.6.1.2, 表E.44)：</strong> `checkValue` 的 0-4 代表的含义（分、合、接地、未知、异常）在系统中统一处理。</li>
<li><strong>接口状态码：</strong> HTTPS API接口返回的 `status` 字段（0成功，1失败）为通用约定。</li>
<li><strong>地址编码：</strong> 遥控、遥信、遥测、遥调的十进制起始地址编码（24901, 3001, 20001, 25001）严格执行，并确保全所唯一且与综自系统地址段区分。</li>
</ul>
<p>系统后台配置管理模块会提供这些数据字典和编码的维护功能，确保其准确性和最新性。</p>
<h3><a id="toc-5-7"></a>5.7 数据存储与管理策略</h3>
<p>数据存储架构（实时Redis、历史MongoDB、配置MySQL，参考“现有设计文档”3.1）旨在高效支持上述规范数据的存储、查询和管理。</p>
<ul>
<li><strong>配置数据 (MySQL)：</strong> 存储所有遵循规范定义的设备信息、点表信息、摄像机预置位、安装区域、拍摄焦点、编码字典等结构化配置数据。表结构设计严格对应“数据规范与接口”中的点表字段。</li>
<li><strong>实时数据 (Redis)：</strong> 缓存最新的遥信、遥测数据，以及设备运行状态、当前报警等，用于快速查询和界面展示。</li>
<li><strong>历史数据 (MongoDB)：</strong> 存储历史遥信、遥测、报警记录、操作日志、巡检结果（含图片/视频元数据及识别结果）、门禁记录等。对于巡检图片和视频文件本身，可采用分布式文件系统存储，数据库中仅存储其元数据和访问路径。</li>
<li><strong>数据一致性：</strong> 通过中心化配置管理和规范化的数据录入流程，确保不同模块和层级间数据的一致性。</li>
<li><strong>数据备份与恢复：</strong> 制定定期的数据备份策略（全量备份、增量备份）和完善的数据恢复预案，保障数据的安全。</li>
<li><strong>数据安全：</strong> 对敏感数据（如用户密码、NVR访问凭证）进行加密存储，数据库访问进行权限控制。</li>
</ul>
<div class="key-points">
<h4>关键要点总结：数据规范遵循说明</h4>
<ul>
<li>全面遵循“数据规范与接口”文档中的各项命名规范（供电设备、摄像机图像显示、安装区域、拍摄焦点）。</li>
<li>严格实现所有基础数据点表（E.2.5中表E.5-E.18）的字段定义、填写要求和地址编码规则。</li>
<li>建立并应用统一的数据字典和编码规范，确保数据含义的一致性。</li>
<li>数据存储方案（MySQL, MongoDB, Redis）支持规范数据的有效管理、查询和持久化。</li>
<li>所有规范的应用旨在提升数据的标准化水平，为主站系统的数据整合和高级应用奠定基础。</li>
</ul>
</div>
<h2><a id="toc-6"></a>6. 系统安全设计</h2>
<p>系统安全是保障变电所辅助监控系统稳定可靠运行、防止非法访问和数据泄露的基石。本设计方案从多个层面构建安全防护体系，参考“现有设计文档”第5章及附录A.3。</p>
<h3><a id="toc-6-1"></a>6.1 安全设计目标与原则</h3>
<p>安全设计的目标是确保系统的机密性、完整性、可用性、可控性和可审查性。</p>
<ul>
<li><strong>机密性（Confidentiality）：</strong> 防止敏感信息泄露给未经授权的实体。</li>
<li><strong>完整性（Integrity）：</strong> 防止数据被未经授权的篡改或损坏，确保数据的准确性和一致性。</li>
<li><strong>可用性（Availability）：</strong> 确保授权用户在需要时能够访问系统和数据。</li>
<li><strong>可控性（Controllability）：</strong> 对系统操作和数据访问进行有效控制和管理。</li>
<li><strong>可审查性（Auditability）：</strong> 对所有安全相关事件和用户行为进行记录，以便审计和追溯。</li>
</ul>
<p>遵循等级保护相关要求（如GB/T 22240定级、GB/T 25070设计）进行安全设计。</p>
<h3><a id="toc-6-2"></a>6.2 网络安全设计</h3>
<p>参考“现有设计文档”附录A.3.1。</p>
<ul>
<li><strong>网络区域划分与隔离：</strong>
<ul>
<li>站控层网络与间隔层（现场设备网络）之间应进行有效隔离，例如通过防火墙或物理隔离。</li>
<li>若与企业办公网或互联网连接，必须通过严格的安全边界防护设备（如防火墙、网闸）。</li>
</ul>
</li>
<li><strong>防火墙策略：</strong>
<ul>
<li>部署防火墙在关键网络节点，实施严格的访问控制策略（ACL）。</li>
<li>遵循最小权限原则，仅开放必要的服务端口。例如，入站规则允许HTTPS (TCP 443)、IEC104 (TCP 2404)、NVR访问 (如TCP 8000)，其他默认拒绝。</li>
</ul>
</li>
<li><strong>入侵检测/防御系统 (IDS/IPS)：</strong>
<ul>
<li>在网络出口或关键服务器群组前部署IDS/IPS，检测和阻止常见的网络攻击，如端口扫描、暴力破解、SQL注入、XSS攻击、异常流量等。</li>
</ul>
</li>
<li><strong>网络设备安全：</strong>
<ul>
<li>交换机、路由器等网络设备应修改默认口令，关闭不必要的服务，定期更新固件。</li>
<li>采用VLAN划分等技术增强网络隔离。</li>
</ul>
</li>
<li><strong>安全传输：</strong>
<ul>
<li>系统与主站之间的API通信强制使用HTTPS (TLS 1.2或更高)。</li>
<li>IEC104通信本身安全性较低，若跨越不信任网络，应考虑VPN等加密隧道技术。</li>
<li>远程维护和管理访问应通过VPN或SSH等安全通道。</li>
</ul>
</li>
</ul>
<h3><a id="toc-6-3"></a>6.3 应用安全设计</h3>
<p>参考“现有设计文档”附录A.3.2。</p>
<ul>
<li><strong>身份认证：</strong>
<ul>
<li>用户登录系统需提供唯一用户名和强密码。密码策略：最小长度（如8位）、复杂度要求（大小写字母+数字+特殊字符）、定期更换（如90天）、历史密码限制。</li>
<li>API接口调用采用Bearer Token认证机制，在每次请求的Header中携带Token。Token具有有效期，并推荐使用HTTPS保障其传输安全。</li>
<li>多次登录失败（如3-5次）应锁定账户一段时间（如30分钟），并可启用验证码机制。</li>
<li>会话管理：设置合理的会话超时时间（如30分钟无操作自动登出），限制并发会话数量。</li>
</ul>
</li>
<li><strong>输入验证与输出编码：</strong>
<ul>
<li>对所有用户输入（包括API请求参数）进行严格的合法性校验（类型、长度、格式、范围），防止恶意输入。</li>
<li>对动态输出到页面的数据进行适当编码，防止XSS（跨站脚本）攻击。</li>
<li>采用参数化查询或ORM框架（如Entity Framework Core）防止SQL注入。</li>
</ul>
</li>
<li><strong>API接口安全：</strong>
<ul>
<li>接口访问频率限制，防止恶意DOS攻击。</li>
<li>对关键操作接口（如控制命令、配置修改）增加二次确认或操作票机制。</li>
<li>敏感信息不在URL参数中传递。</li>
<li>防止请求重放和篡改，例如通过时间戳和签名机制。</li>
</ul>
</li>
<li><strong>安全开发生命周期 (SDL)：</strong> 在软件设计、开发、测试、部署各阶段融入安全考虑。</li>
</ul>
<h3><a id="toc-6-4"></a>6.4 数据安全设计</h3>
<p>参考“现有设计文档”5.4.3。</p>
<ul>
<li><strong>数据传输加密：</strong>
<ul>
<li>所有通过公网或不信任网络传输的敏感数据均应加密，如HTTPS采用TLS/SSL。</li>
</ul>
</li>
<li><strong>数据存储加密：</strong>
<ul>
<li>对数据库中存储的敏感数据（如用户密码、系统访问凭证）进行哈希加盐或加密处理（如AES-256）。</li>
<li>考虑对整个数据库或关键数据文件进行加密。</li>
</ul>
</li>
<li><strong>数据备份与恢复：</strong>
<ul>
<li>制定完善的数据备份策略（如每日增量、每周全量备份），备份介质异地存放。</li>
<li>定期进行数据恢复演练，确保备份的有效性。</li>
<li>数据库事务机制保证操作的原子性和一致性。</li>
</ul>
</li>
<li><strong>数据防泄漏：</strong>
<ul>
<li>通过严格的权限控制，限制用户对数据的访问范围。</li>
<li>对导出数据的功能进行控制和审计。</li>
<li>重要报表和数据文件可添加水印。</li>
</ul>
</li>
<li><strong>密钥管理：</strong>
<ul>
<li>安全存储和管理用于加密的密钥，实施密钥轮换机制。</li>
<li>密钥的生成、分发、存储、使用、销毁全生命周期管理。</li>
</ul>
</li>
</ul>
<h3><a id="toc-6-5"></a>6.5 权限管理设计</h3>
<p>参考“现有设计文档”5.4.2及10.2，并重点结合“数据规范与接口”E.7。</p>
<ul>
<li><strong>基于角色的访问控制 (RBAC)：</strong>
<ul>
<li>定义不同的用户角色（如系统管理员、运维值班员、巡检操作员、只读用户等）。</li>
<li>为每个角色分配相应的操作权限（如查看、控制、配置、巡检、报警确认等）和数据范围权限（如可访问的所亭、设备类型）。</li>
<li>用户被分配一个或多个角色，从而继承这些角色的权限。</li>
<li>权限控制应细化到菜单、按钮、API接口层面。</li>
</ul>
</li>
<li><strong>摄像机控制权限管理 (重点)：</strong>
<ul>
<li>严格遵循“数据规范与接口”E.7中定义的摄像机鉴权机制。</li>
<li>系统作为申请方（当进行本地手动控制或自动巡检时）或作为接口提供方（响应主站的控制权变更通知），与鉴权中心进行交互。</li>
<li>**权限级别应用：** 系统在申请摄像机控制权时，需根据操作类型（如本地所级手动调阅对应E.7.1.1表E.46的第十一级`SJDY`，所内视频巡检对应第十二级`SJXJ`）向鉴权中心上报对应的控制或触发条件编码。</li>
<li><strong>冲突处理：** 若申请的摄像机已被更高优先级的操作占用，鉴权中心将拒绝申请并返回预计释放时间。本地系统应能处理此种情况，并提示用户。</strong></li>
<li><strong>权限释放：** 控制完毕后，应主动调用E.7.1.3接口释放摄像机控制权。若未主动释放，2分钟后鉴权中心自动释放。</strong></li>
<li><strong>变更通知：** 辅助监控系统需实现E.7.1.4接口，接收鉴权中心关于摄像机控制权变更的通知，并据此调整本地的巡检任务或用户操作状态。</strong></li>
</ul>
</li>
<li><strong>最小权限原则：</strong> 用户仅被授予完成其工作所必需的最小权限。</li>
<li><strong>权限分离：</strong> 管理员权限与操作员权限分离，审计权限与被审计对象分离。</li>
</ul>
<h3><a id="toc-6-6"></a>6.6 安全审计</h3>
<p>参考“现有设计文档”10.10.3。</p>
<ul>
<li><strong>日志记录范围：</strong>
<ul>
<li>用户登录/登出、重要操作（如设备控制、参数配置修改、权限变更）。</li>
<li>系统关键事件（启动、停止、严重故障）。</li>
<li>安全相关事件（如登录失败尝试、权限申请/释放、非法访问尝试）。</li>
<li>API接口调用日志（可选，用于排错和审计）。</li>
<li>数据库操作日志（通过数据库自身审计功能或应用层面记录）。</li>
</ul>
</li>
<li><strong>日志内容：</strong> 日志应至少包含时间戳、用户ID、操作对象、操作类型、操作结果、来源IP等信息。</li>
<li><strong>日志保护：</strong>
<ul>
<li>日志文件应防止未授权访问和篡改。</li>
<li>定期备份日志文件到安全存储位置。</li>
<li>日志存储满足一定的保留期限要求（如至少6个月或根据行业规定）。</li>
</ul>
</li>
<li><strong>日志审计与分析：</strong>
<ul>
<li>提供日志查询和分析工具，支持按多种条件检索。</li>
<li>定期对安全日志进行审计，发现异常行为和潜在威胁。</li>
<li>可集成到SIEM（安全信息和事件管理）平台进行统一分析。</li>
</ul>
</li>
</ul>
<div class="key-points">
<h4>关键要点总结：系统安全设计</h4>
<ul>
<li>采用纵深防御策略，覆盖网络、应用、数据、权限、审计等多个层面。</li>
<li>网络层面强调区域隔离、防火墙、IDS/IPS和安全传输。</li>
<li>应用层面注重身份认证、输入验证、API安全。</li>
<li>数据层面关注传输与存储加密、备份恢复。</li>
<li>权限管理采用RBAC模型，并深度整合“数据规范与接口”E.7的摄像机鉴权机制。</li>
<li>全面的安全审计日志为事后追溯和安全分析提供依据。</li>
</ul>
</div>
<h2><a id="toc-7"></a>7. 部署与运维考虑</h2>
<p>本章节阐述系统的推荐部署方案、软硬件环境要求以及后续运行维护和监控方面的考虑，旨在确保系统能够顺利部署、稳定运行并易于维护。参考“现有设计文档”第6章和第8章及附录A.4。</p>
<h3><a id="toc-7-1"></a>7.1 部署架构方案</h3>
<p>参考“现有设计文档”6.1标准部署架构，系统通常采用集中式或分布式部署方案，具体取决于变电所规模和可靠性要求。</p>
<ul>
<li><strong>典型部署模型 (站控层)：</strong>
<ul>
<li><strong>应用服务器集群/主备：</strong> 部署核心后台服务（配置管理、用户管理、数据管理API等）、巡检服务、识别服务。可采用负载均衡或主备方式提高可用性。</li>
<li><strong>数据服务器：</strong> 运行IEC104服务，负责与现场设备（通过通信管理机）和主站的数据交互。</li>
<li><strong>数据库服务器集群/主备：</strong> 部署MySQL（配置库）、MongoDB（历史库）、Redis（缓存/消息队列）。可采用主从复制、集群等方式保证数据可靠性和读写性能。</li>
<li><strong>视频服务器/NVR：</strong> 负责视频流的接入、存储、转发。可采用专用NVR设备或服务器部署视频管理软件。</li>
<li><strong>操作员工作站/客户端：</strong> 运行Qt桌面客户端，用于本地监控操作。</li>
<li><strong>Web配置管理端：</strong> 可部署在应用服务器上，通过浏览器访问。</li>
<li><strong>通信管理机：</strong> 作为间隔层设备与站控层服务器之间的数据网关。</li>
</ul>
</li>
<li><strong>网络规划：</strong>
<ul>
<li>站控层内部采用高速以太网（千兆或更高），核心交换机可采用冗余配置。</li>
<li>与间隔层设备的连接根据设备类型可采用以太网、RS485总线等。</li>
<li>与主站的连接通过专用网络或安全通道。</li>
</ul>
</li>
<li><strong>高可用性考虑：</strong>
<ul>
<li>关键服务器（应用、数据、数据库）采用双机热备或集群方案。</li>
<li>网络链路冗余。</li>
<li>存储系统采用RAID等技术防止单点故障。</li>
</ul>
</li>
</ul>
<div class="figure-title">图7-1 典型站控层部署示意图</div>
<line text="" x1psixmdaiihkxpsi1mcigedi9ijm1mcigeti9ijcwiibzdhjva2u9iim1ntuiihn0cm9rzs13awr0ad0ims41iibtyxjrzxitzw5kpsj1cmwoi2fycm93agvhzdupii8+phjly3qged0imtaiihk9ije1mcigd2lkdgg9ije4mcigagvpz2h0psi2mcigcng9ijuiigzpbgw9iinmzmzmy2miihn0cm9rzt0ii2njy2mwmcivpjx0zxh0ihg9ijewmciget0imtcwiib0zxh0lwfuy2hvcj0ibwlkzgxlij5应用服务器(集群="" 主备)<=""><text mongodb="" ntgwiib5psixotaiigzvbnqtc2l6zt0imtaiihrlehqtyw5jag9ypsjtawrkbguipkmysql="" redis<="" text="" x="MTAwIiB5PSIxOTAiIGZvbnQtc2l6ZT0iMTAiIHRleHQtYW5jaG9yPSJtaWRkbGUiPk后台/巡检/识别服务&lt;/text&gt;&lt;line x1PSIxMDAiIHkxPSIxNTAiIHgyPSIzMzAiIHkyPSIxMTAiIHN0cm9rZT0iIzU1NSIgc3Ryb2tlLXdpZHRoPSIxLjUiLz48cmVjdCB4PSIyNTAiIHk9IjE1MCIgd2lkdGg9IjE4MCIgaGVpZ2h0PSI2MCIgcng9IjUiIGZpbGw9IiNmZmZmY2MiIHN0cm9rZT0iI2NjY2MwMCIvPjx0ZXh0IHg9IjM0MCIgeT0iMTgwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5数据服务器(主/备)&lt;/text&gt;&lt;line x1PSIzNDUiIHkxPSIxNTAiIHgyPSIzNjAiIHkyPSIxMTAiIHN0cm9rZT0iIzU1NSIgc3Ryb2tlLXdpZHRoPSIxLjUiLz48cmVjdCB4PSI0OTAiIHk9IjE1MCIgd2lkdGg9IjE4MCIgaGVpZ2h0PSI2MCIgcng9IjUiIGZpbGw9IiNmZmZmY2MiIHN0cm9rZT0iI2NjY2MwMCIvPjx0ZXh0IHg9IjU4MCIgeT0iMTcwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5数据库服务器(集群/主备)&lt;/text&gt;&lt;text x="><line text="" x1psi1nzuiihkxpsixntaiihgypsiznzuiihkypsixmtaiihn0cm9rzt0iizu1nsigc3ryb2tllxdpzhropsixljuilz48cmvjdcb4psixmciget0imjmwiib3awr0ad0imtgwiibozwlnahq9ijqwiibyed0insigzmlsbd0ii2ywzmzmziigc3ryb2tlpsijy2nkzgzmii8+phrlehqged0imtawiib5psiyntuiihrlehqtyw5jag9ypsjtawrkbguipk操作员工作站(qt)<=""><line text="" x1psixmdaiihkxpsiymzaiihgypsizmzaiihkypsixmtaiihn0cm9rzt0iizu1nsigc3ryb2tllxdpzhropsixljuilz48cmvjdcb4psiyntaiihk9ijizmcigd2lkdgg9ije4mcigagvpz2h0psi0mcigcng9ijuiigzpbgw9iinmmmy4zmyiihn0cm9rzt0iiznjyyivpjx0zxh0ihg9ijm0mciget0imju1iib0zxh0lwfuy2hvcj0ibwlkzgxlij5web配置管理(vue)<=""><line text="" x1psiznduiihkxpsiymzaiihgypsiznjaiihkypsixmtaiihn0cm9rzt0iizu1nsigc3ryb2tllxdpzhropsixljuilz48cmvjdcb4psi0otaiihk9ijizmcigd2lkdgg9ije4mcigagvpzht9ijqwiibyed0insigzmlsbd0ii2zmzdbkmcigc3ryb2tlpsijy2njyjawii8+phrlehqged0intgwiib5psiyntuiihrlehqtyw5jag9ypsjtawrkbguipknvr="" 视频服务器<=""><line text="" x1psi1nzuiihkxpsiymzaiihgypsiznzuiihkypsixmtaiihn0cm9rzt0iizu1nsigc3ryb2tllxdpzhropsixljuilz48cmvjdcb4psiyntaiihk9ijmxmcigd2lkdgg9ijiwmcigagvpz2h0psi0mcigcng9ijuiigzpbgw9iinlmgyyzmyiihn0cm9rzt0iizrhotbkyyivpjx0zxh0ihg9ijm1mciget0imzm1iib0zxh0lwfuy2hvcj0ibwlkzgxlij5间隔层接入交换机="" 通信管理机<=""><line nvr<="" text="" x1psizntaiihkxpsixmtaiihgypsizntaiihkypsizmtaiihn0cm9rzt0iizu1nsigc3ryb2tllxdpzhropsixljuiig1hcmtlci1lbmq9invybcgjyxjyb3dozwfknskilz48zyb0cmfuc2zvcm09inryyw5zbgf0zsgwldm3mckipjxyzwn0ihg9ijuwiib5psiwiib3awr0ad0imtewiibozwlnahq9ijuwiibyed0insigzmlsbd0ii2vmzwzlziigc3ryb2tlpsijy2njy2njii8+phrlehqged0imta1iib5psiynsigdgv4dc1hbmnob3i9im1pzgrszsi+摄像头=""><line text="" x1psixmduiihkxpsi1mcigedi9ijm1mcigeti9ii02mcigc3ryb2tlpsijnzc3iibtyxjrzxitzw5kpsj1cmwoi2fycm93agvhzdupii8+phjly3qged0imjewiib5psiwiib3awr0ad0imtewiibozwlnahq9ijuwiibyed0insigzmlsbd0ii2vmzwzlziigc3ryb2tlpsijy2njy2njii8+phrlehqged0imjy1iib5psiynsigdgv4dc1hbmnob3i9im1pzgrszsi+安防设备<=""><line text="" x1psiynjuiihkxpsi1mcigedi9ijm1mcigeti9ii02mcigc3ryb2tlpsijnzc3iibtyxjrzxitzw5kpsj1cmwoi2fycm93agvhzdupii8+phjly3qged0imzcwiib5psiwiib3awr0ad0imtewiibozwlnahq9ijuwiibyed0insigzmlsbd0ii2vmzwzlziigc3ryb2tlpsijy2njy2njii8+phrlehqged0indi1iib5psiynsigdgv4dc1hbmnob3i9im1pzgrszsi+环境传感器<=""><line text="" x1psi0mjuiihkxpsi1mcigedi9ijm1mcigeti9ii02mcigc3ryb2tlpsijnzc3iibtyxjrzxitzw5kpsj1cmwoi2fycm93agvhzdupii8+phjly3qged0intmwiib5psiwiib3awr0ad0imtewiibozwlnahq9ijuwiibyed0insigzmlsbd0ii2vmzwzlziigc3ryb2tlpsijy2njy2njii8+phrlehqged0intg1iib5psiynsigdgv4dc1hbmnob3i9im1pzgrszsi+其他设备<=""><line x1psi1oduiihkxpsi1mcigedi9ijm1mcigeti9ii02mcigc3ryb2tlpsijnzc3iibtyxjrzxitzw5kpsj1cmwoi2fycm93agvhzdupii8+pc9npjwvzz48l3n2zz4=" alt=" 典型站控层部署示意图"=""></line>
<h3><a id="toc-7-2"></a>7.2 硬件环境要求</h3>
<p>参考“现有设计文档”附录A.4.1。硬件配置需满足系统性能、容量和可靠性要求，具体配置应根据变电所规模和监控点数量进行调整。</p>
<p class="table-title">表7-1 典型服务器硬件配置要求</p>
<table>
<thead>
<tr><th>服务器类型</th><th>CPU</th><th>内存</th><th>存储 (系统盘/数据盘)</th><th>网卡</th><th>操作系统 (推荐)</th></tr>
</thead>
<tbody>
<tr><td>应用服务器</td><td>Intel Xeon 8核心 2.4GHz或以上</td><td>32GB DDR4 ECC</td><td>SSD 500GB / HDD 2TB (RAID)</td><td>双千兆以太网卡</td><td>Windows Server 2019/2022 或 Linux (如CentOS/Ubuntu Server)</td></tr>
<tr><td>数据库服务器</td><td>Intel Xeon 8核心 2.4GHz或以上</td><td>64GB DDR4 ECC</td><td>SSD 1TB (RAID) / SAS 4TB (RAID)</td><td>双千兆以太网卡</td><td>Windows Server 2019/2022 或 Linux</td></tr>
<tr><td>数据服务器 (IEC104)</td><td>Intel Xeon 4核心 2.2GHz或以上</td><td>16GB DDR4 ECC</td><td>SSD 250GB / HDD 1TB</td><td>双千兆以太网卡</td><td>Windows Server 2019/2022 或 Linux</td></tr>
<tr><td>视频服务器/NVR</td><td colspan="4">根据并发视频路数、存储时长、分辨率等选型，推荐采用专业NVR设备或高性能服务器配置。</td><td>专用嵌入式系统或Windows Server</td></tr>
<tr><td>操作员工作站</td><td>Intel Core i5/i7 桌面级</td><td>16GB DDR4</td><td>SSD 500GB</td><td>千兆以太网卡</td><td>Windows 10/11 专业版</td></tr>
</tbody>
</table>
<ul>
<li><strong>网络设备：</strong> 核心交换机、接入交换机应选择企业级产品，支持VLAN、QoS等功能，提供足够的端口密度和背板带宽。</li>
<li><strong>存储设备：</strong> 可采用DAS、NAS或SAN，根据数据量和性能需求选择。推荐对重要数据盘做RAID（如RAID1, RAID5, RAID10）。</li>
<li><strong>机房环境：</strong> 遵循GB/T 2887《计算机场地通用规范》，确保稳定的供电（UPS）、温湿度控制、防尘、防雷接地等。</li>
</ul>
<h3><a id="toc-7-3"></a>7.3 软件环境要求</h3>
<p>参考“现有设计文档”附录A.4.2。</p>
<ul>
<li><strong>操作系统：</strong>
<ul>
<li>服务器端：推荐使用 Windows Server 2019/2022 或主流Linux发行版（如CentOS 7/8, Ubuntu Server 20.04 LTS）。</li>
<li>客户端：Windows 10/11 专业版。</li>
</ul>
</li>
<li><strong>数据库软件：</strong>
<ul>
<li>MySQL 8.0 或更高版本。</li>
<li>MongoDB 4.2 或更高版本。</li>
<li>Redis 4.0 或更高版本。</li>
</ul>
</li>
<li><strong>Web服务器 (若部署Web配置端)：</strong>
<ul>
<li>ASP.NET Core应用可使用Kestrel内置服务器，或反向代理至Nginx (1.18+) 或 IIS。</li>
</ul>
</li>
<li><strong>运行时环境与依赖库：</strong>
<ul>
<li>.NET 6.0 (或更高) Runtime (用于ASP.NET Core后端服务)。</li>
<li>C++运行时库 (用于Qt客户端和部分服务)。</li>
<li>Node.js (用于Vue.js前端构建，若需要)。</li>
<li>Python环境 (若AI识别服务使用Python开发，如PyTorch)。</li>
<li>具体第三方组件依赖（如OpenCV、lib60870等）的版本需与系统兼容。</li>
</ul>
</li>
<li><strong>安全软件：</strong> 服务器和客户端均应安装防病毒软件，并保持病毒库更新。</li>
</ul>
<h3><a id="toc-7-4"></a>7.4 系统监控与告警</h3>
<p>为保障系统稳定运行，需建立完善的运维监控体系。参考“现有设计文档”第8章和附录A.5.2。</p>
<ul>
<li><strong>监控范围：</strong>
<ul>
<li><strong>基础设施监控：</strong> 服务器硬件状态（CPU、内存、磁盘使用率、网络I/O、温度）、网络设备状态（连通性、流量）。</li>
<li><strong>操作系统监控：</strong> 关键进程、服务运行状态、系统日志。</li>
<li><strong>应用服务监控：</strong> 各核心服务（后台API、数据服务、巡检服务、识别服务）的运行状态、接口响应时间、错误率、TPS/QPS。</li>
<li><strong>数据库监控：</strong> 连接数、查询性能、慢查询、磁盘空间、复制状态（若有主从）。</li>
<li><strong>业务指标监控：</strong> 数据采集成功率、报警响应及时率、视频调阅成功率、巡检任务完成率。</li>
</ul>
</li>
<li><strong>监控工具：</strong>
<ul>
<li>可采用开源监控方案如 Zabbix, Prometheus + Grafana。</li>
<li>对于日志集中管理和分析，可使用ELK Stack (Elasticsearch, Logstash, Kibana)。</li>
<li>分布式链路追踪可采用 Jaeger 或 SkyWalking。</li>
</ul>
</li>
<li><strong>告警机制：</strong>
<ul>
<li><strong>阈值设定：</strong> 对关键监控指标设置合理的告警阈值（如CPU使用率&gt;85%，磁盘空间&lt;10%）。</li>
<li><strong>告警分级：</strong> 根据事件严重程度分为提示、警告、严重等级别。</li>
<li><strong>告警通知：</strong> 支持通过邮件、短信、即时通讯工具（如钉钉、企业微信）、界面弹窗等多种方式通知运维人员。</li>
<li><strong>告警抑制与收敛：</strong> 防止告警风暴，对重复告警或关联告警进行抑制和收敛。</li>
</ul>
</li>
</ul>
<div class="figure-title">图7-2 关键性能指标(KPI)监控示例 (参考“现有设计文档”A.5.2)</div>
<div class="chart-container" id="kpiMonitoringChart"></div>
<h3><a id="toc-7-5"></a>7.5 维护与升级</h3>
<ul>
<li><strong>日常维护：</strong>
<ul>
<li>定期检查系统运行状态、日志信息。</li>
<li>数据库维护（如性能优化、索引重建、空间清理）。</li>
<li>数据备份检查与恢复演练。</li>
<li>操作系统和应用软件安全补丁更新。</li>
</ul>
</li>
<li><strong>配置管理：</strong>
<ul>
<li>系统所有配置项（设备参数、报警阈值、用户权限、网络设置等）应进行版本控制和变更管理。</li>
<li>重要配置变更前应进行备份和审批。</li>
</ul>
</li>
<li><strong>故障排除：</strong>
<ul>
<li>提供详细的故障排除手册和应急预案。</li>
<li>建立故障处理流程，明确责任人和处理时限。</li>
</ul>
</li>
<li><strong>软件升级：</strong>
<ul>
<li>制定清晰的软件版本发布和升级策略。</li>
<li>升级前进行充分测试，并在非业务高峰期进行。</li>
<li>提供回滚方案以应对升级失败。</li>
<li>客户端软件可考虑在线自动升级或提示升级功能。</li>
</ul>
</li>
<li><strong>技术支持：</strong> 明确系统开发商或集成商的技术支持渠道和SLA。</li>
</ul>
<div class="key-points">
<h4>关键要点总结：部署与运维考虑</h4>
<ul>
<li>部署架构应考虑高可用性，关键服务器和网络设备采用冗余配置。</li>
<li>软硬件环境需满足系统性能和容量需求，并预留一定扩展空间。</li>
<li>建立全面的系统监控体系，覆盖基础设施、应用服务和业务指标。</li>
<li>完善的告警机制能及时通知运维人员处理异常。</li>
<li>规范的维护流程、配置管理和升级策略是保障系统长期稳定运行的关键。</li>
</ul>
</div>
<h2><a id="toc-8"></a>8. 性能与可扩展性设计</h2>
<p>系统的性能和可扩展性是衡量其先进性和生命周期的重要标准。本设计在架构、编码、资源利用等多个层面进行了优化，以满足当前性能指标并适应未来发展需求。参考“现有设计文档”第7章及相关Q/CR标准。</p>
<h3><a id="toc-8-1"></a>8.1 系统性能指标</h3>
<p>系统性能指标严格参照Q/CR 1029-2024、Q/CR 796-2020等相关标准，并结合“现有设计文档”7.1节的要求。以下为部分核心指标：</p>
<h4>8.1.1 系统可用性指标 (参考“现有设计文档”7.1.1)</h4>
<div class="chart-container" id="availabilityMetricsChart"></div>
<p class="table-title">表8-1 系统可用性指标要求</p>
<table>
<thead><tr><th>指标项目</th><th>标准要求</th><th>设计目标</th><th>实现措施</th></tr></thead>
<tbody>
<tr><td>系统可用率</td><td>≥99.99%</td><td>≥99.99%</td><td>冗余架构设计, 故障快速切换</td></tr>
<tr><td>平均无故障时间 (MTBF)</td><td>≥30000h</td><td>≥35000h</td><td>高可靠组件选型, 优化设计</td></tr>
<tr><td>硬件MTBF</td><td>≥50000h</td><td>≥60000h</td><td>工业级硬件, 良好散热</td></tr>
<tr><td>平均修复时间 (MTTR)</td><td>≤30min</td><td>≤15min</td><td>快速故障诊断与定位, 备件充足</td></tr>
<tr><td>数据恢复时间 (RTO)</td><td>≤30min</td><td>≤15min</td><td>热备份与快速恢复机制</td></tr>
<tr><td>数据恢复点 (RPO)</td><td>≤15min</td><td>≤5min</td><td>实时或准实时数据备份</td></tr>
</tbody>
</table>
<h4>8.1.2 实时性指标要求 (参考“现有设计文档”7.1.2)</h4>
<div class="chart-container" id="realtimeMetricsChart"></div>
<p class="table-title">表8-2 实时性指标要求</p>
<table>
<thead><tr><th>指标项目</th><th>标准要求</th><th>设计目标</th><th>技术保障措施</th></tr></thead>
<tbody>
<tr><td>视频控制切换响应时间</td><td>≤1s</td><td>≤500ms</td><td>优化视频流媒体服务, 专用视频通道</td></tr>
<tr><td>控制命令响应时间 (遥控/遥调)</td><td>≤1s</td><td>≤500ms</td><td>高效实时通信协议 (IEC104), 优化处理逻辑</td></tr>
<tr><td>画面显示延迟时间 (实时视频)</td><td>≤1s</td><td>≤500ms</td><td>低延迟编解码, 优化网络传输与渲染</td></tr>
<tr><td>事件报警响应时间 (从发生到显示)</td><td>≤1s</td><td>≤300ms</td><td>事件驱动机制, 高效消息队列</td></tr>
<tr><td>报警信息上送主站时间</td><td>≤2s (一般) / ≤1s (重要)</td><td>≤1s / ≤500ms</td><td>IEC104优化, 优先处理高优先级报警</td></tr>
<tr><td>系统时钟精度 (与标准时钟同步)</td><td>≤10ms</td><td>≤5ms</td><td>NTP/PTP高精度对时, 硬件时钟支持</td></tr>
<tr><td>SOE分辨率 (事件顺序记录)</td><td>≤2ms</td><td>≤1ms</td><td>硬件时标采集, 高速事件处理</td></tr>
<tr><td>数据采集周期 (遥测/遥信)</td><td>≤5s (可配置)</td><td>≤3s (典型)</td><td>多线程并发采集, 异步处理</td></tr>
<tr><td>历史数据查询响应 (典型查询)</td><td>≤3s</td><td>≤2s</td><td>数据库索引优化, 查询缓存</td></tr>
</tbody>
</table>
<p class="note">“数据规范与接口”中也隐含有对接口响应时间的要求，例如巡检停留时间不小于5秒，意味着相关控制和图像采集必须在此时间内完成。</p>
<h4>8.1.3 数据质量指标 (参考“现有设计文档”7.1.3)</h4>
<p class="table-title">表8-3 数据质量指标</p>
<table>
<thead><tr><th>指标项目</th><th>标准要求</th><th>设计目标</th><th>保障措施</th></tr></thead>
<tbody>
<tr><td>遥测综合误差</td><td>≤0.5% (传感器精度满足要求前提下)</td><td>≤0.3%</td><td>高精度传感器, 定期校准, 传输校验</td></tr>
<tr><td>遥测合格率 (有效数据)</td><td>≥99.99%</td><td>≥99.99%</td><td>数据质量戳, 异常值过滤, 通信校验</td></tr>
<tr><td>遥信响应正确率</td><td>100%</td><td>100%</td><td>变位检测算法优化, 防抖动处理, 双确认机制</td></tr>
<tr><td>遥控/遥调正确率</td><td>100%</td><td>100%</td><td>操作前选择-操作后确认 (SBO), 反校机制</td></tr>
<tr><td>数据完整性 (不丢失)</td><td>≥99.9%</td><td>≥99.95%</td><td>可靠传输协议, 校验码, 事务管理, 冗余存储</td></tr>
<tr><td>数据一致性 (各节点)</td><td>≥99.9%</td><td>≥99.95%</td><td>分布式事务管理 (若适用), 数据同步机制</td></tr>
</tbody>
</table>
<h4>8.1.4 其他性能指标</h4>
<ul>
<li><strong>并发用户数：</strong> 系统应能支持设计规格的并发用户访问（如本地客户端、Web配置端、主站接口调用），典型目标≥10-20个并发操作用户（站级），更高并发的API调用。</li>
<li><strong>数据处理能力：</strong>
<ul>
<li>每秒处理遥测量点数：根据变电所规模，如数百至数千点/秒。</li>
<li>每秒处理遥信变位数：峰值处理能力，如数十至上百条/秒。</li>
<li>视频并发路数：实时浏览、录像、回放、智能分析的总并发路数，如32/64/128路。</li>
</ul>
</li>
<li><strong>存储容量：</strong> 历史数据存储时长（如遥测1年、报警3年、录像30天）、巡检图片视频存储量等，根据需求配置，系统应支持在线扩容。</li>
</ul>
<h3><a id="toc-8-2"></a>8.2 性能优化设计</h3>
<p>为达到上述性能指标，系统从多个层面进行性能优化：</p>
<ul>
<li><strong>架构层面：</strong>
<ul>
<li><strong>服务化与异步化：</strong> 将耗时操作（如智能识别、报表生成、批量数据处理）设计为异步任务，通过消息队列（如Redis Pub/Sub）解耦，避免阻塞主流程。后台服务采用ASP.NET Core，其本身具有良好的异步处理能力。</li>
<li><strong>分布式缓存：</strong> 广泛使用Redis作为分布式缓存，缓存热点数据（如设备配置、最新遥测值、用户会话），减少数据库访问压力，提高响应速度。</li>
<li><strong>负载均衡：</strong> 对无状态的应用服务（如API网关、Web服务）可部署多个实例，通过负载均衡器分发请求，提高并发处理能力和可用性。</li>
<li><strong>数据库读写分离：</strong> 对于读多写少的场景（如历史数据查询），可配置数据库主从复制，将读请求分发到从库，减轻主库压力。</li>
</ul>
</li>
<li><strong>数据处理与存储层面：</strong>
<ul>
<li><strong>数据库优化：</strong> 合理设计数据库表结构和索引；优化SQL查询语句，避免全表扫描；采用连接池技术复用数据库连接。对于MongoDB，合理设计文档结构和索引策略。</li>
<li><strong>数据压缩：</strong> 对历史数据和传输数据（如图片、视频）采用有效的压缩算法，减少存储空间和网络带宽占用。</li>
<li><strong>批量处理：</strong> 对数据的批量写入、更新操作进行优化，减少I/O次数。</li>
</ul>
</li>
<li><strong>编码与算法层面：</strong>
<ul>
<li><strong>高效算法：</strong> 关键算法（如智能识别、巡检路径规划）选择或设计高效实现。</li>
<li><strong>并发与并行编程：</strong> 对于可并行的计算密集型任务，利用多核CPU进行并行处理。对于I/O密集型操作，采用异步编程模型（async/await）。</li>
<li><strong>内存管理：</strong> 优化对象生命周期管理，避免内存泄漏，合理使用内存池或对象池。</li>
<li><strong>前端优化：</strong> 客户端（Qt/Vue）界面渲染优化，数据懒加载，减少不必要的重绘。Vue.js采用虚拟DOM和组件化开发，本身有助于性能提升。</li>
</ul>
</li>
<li><strong>网络层面：</strong>
<ul>
<li><strong>协议选择：</strong> 选择高效的网络通信协议，如对实时性要求高的采用UDP（需自行保证可靠性）或优化的TCP参数。视频传输采用RTSP/RTMP，并支持H.264/H.265高效编码。</li>
<li><strong>带宽优化：</strong> 主子码流切换，根据网络状况自适应调整视频质量。</li>
</ul>
</li>
</ul>
<h3><a id="toc-8-3"></a>8.3 系统可扩展性设计</h3>
<p>系统设计充分考虑未来的业务发展和技术升级，具备良好的可扩展性：</p>
<ul>
<li><strong>模块化设计：</strong> 系统各功能模块高度解耦，遵循“高内聚、低耦合”原则。新增功能模块或升级现有模块对其他模块影响最小。</li>
<li><strong>标准化接口：</strong> 内部模块间、系统与外部系统间的接口采用标准协议和规范定义（如RESTful API，IEC104，“数据规范与接口”文档）。方便未来接入新的设备类型、第三方系统或扩展新的应用。</li>
<li><strong>配置驱动：</strong> 大部分业务逻辑和参数（如报警规则、巡检计划、设备点表、界面元素）通过配置实现，而非硬编码。业务需求变化时，可通过修改配置快速适应，减少代码修改。</li>
<li><strong>水平扩展 (Scale Out)：</strong>
<ul>
<li>无状态应用服务（如Web API服务、识别服务）可通过增加服务器节点进行水平扩展，由负载均衡器分发流量。</li>
<li>分布式数据库（如MongoDB集群、Redis集群）支持通过增加节点来提升存储容量和处理能力。</li>
<li>消息队列集群支持更高的消息吞吐量。</li>
</ul>
</li>
<li><strong>垂直扩展 (Scale Up)：</strong>
<ul>
<li>通过提升单个服务器节点的硬件配置（如增加CPU、内存、升级SSD）来增强处理能力。</li>
</ul>
</li>
<li><strong>数据模型扩展性：</strong> 数据库表结构设计预留扩展字段或采用灵活的NoSQL数据库（如MongoDB），方便未来增加新的数据属性。</li>
<li><strong>协议适配层：</strong> 对于设备接入，设计协议适配层，当需要支持新型设备或私有协议时，只需开发新的协议适配插件，无需修改核心业务逻辑。</li>
<li><strong>技术栈升级：</strong> 采用主流和有持续社区支持的技术栈，便于未来进行技术升级和迁移。</li>
</ul>
<div class="key-points">
<h4>关键要点总结：性能与可扩展性设计</h4>
<ul>
<li>系统性能指标全面对标Q/CR等行业标准，关注可用性、实时性和数据质量。</li>
<li>通过服务化架构、异步处理、分布式缓存、数据库优化、高效算法等多维度手段进行性能优化。</li>
<li>可扩展性设计体现在模块化、标准化接口、配置驱动、水平/垂直扩展能力、数据模型灵活性等方面。</li>
<li>目标是构建一个既能满足当前需求，又能适应未来发展的高性能、高可扩展的辅助监控系统。</li>
</ul>
</div>
<h2><a id="toc-9"></a>9. 附录</h2>
<h3><a id="toc-9-1"></a>9.1 参考文献</h3>
<ul>
<li>《变电所辅助监控系统详细设计》（项目内部资料）</li>
<li>《数据规范与接口》（项目内部资料）</li>
<li>Q/CR 1029-2024 《牵引变电所辅助监控系统技术规范》</li>
<li>Q/CR 796-2020 《牵引变电所辅助监控系统主站技术规范》</li>
<li>DL/T 634.5104-2002 《远动设备及系统 第5-104部分：传输协议 网络任务》 (IEC 60870-5-104)</li>
<li>GB/T 28181-2016 《公共安全视频监控联网系统信息传输、交换、控制技术要求》</li>
<li>IEC 61850 《变电站通信网络和系统》系列标准</li>
<li>GB/T 2887-2011 《计算机场地通用规范》</li>
<li>GB 50174-2017 《数据中心设计规范》</li>
<li>GB/T 22240-2020 《信息安全技术 网络安全等级保护定级指南》</li>
<li>GB/T 25070-2019 《信息安全技术 网络安全等级保护安全设计技术要求》</li>
<li>《铁路牵引供电设备设施单元划分、编码暂行规范》 (在供电设备类型名称引用时提及)</li>
</ul>
<h3><a id="toc-9-2"></a>9.2 其他必要附件 (示例)</h3>
<p>本部分可根据实际需要补充更详细的图表示例、关键配置片段等。例如：</p>
<h4>9.2.1 详细JSON请求/响应样例 (补充)</h4>
<p><strong>示例：下发巡检计划接口 (E.5.3.1) 请求体JSON片段 (更详细)：</strong></p>
<pre><code class="json">
{
  "name": "主变及高压室日常巡检",
  "code": "PLAN_20250601_001",
  "subName": "XX变电所",
  "centerType": "DUAN", // DUAN:段级主站, JU:局级主站
  "residenceTime": 10, // 默认停留时间 (秒)
  "cycle": {
    "weeks": "1,3,5", // 周一,周三,周五 (1-7表示周一至周日)
    "startDate": "", // 周期巡检时为空
    "startTime": "09:30" // 启动时间
  },
  "points": [
    {
      "seq": "1",
      "camName": "主变区_1号主变北侧_球机",
      "presetCode": "P001", // 对应1号主变全景
      "pictureNum": 3,
      "residenceTime": 15 // 此点位特定停留时间
    },
    {
      "seq": "2",
      "camName": "主变区_1号主变北侧_球机",
      "presetCode": "P002", // 对应1号主变A相套管
      "pictureNum": 1,
      "residenceTime": 10
    },
    {
      "seq": "3",
      "camName": "27.5kV高压室_机器人_导轨摄像机", // 示例：导轨巡检摄像机
      "presetCode": "RP005", // 对应导轨机器人移动到某位置后的摄像机预置位
      "pictureNum": 2
    }
    // ... 更多巡检点
  ]
}
        </code></pre>
<h4>9.2.2 关键数据库表结构设计 (概念性)</h4>
<p><strong>示例：`DeviceConfig` (设备配置表 - 简化，基于MySQL的配置库)</strong></p>
<pre><code class="sql">
CREATE TABLE DeviceConfig (
    DeviceID VARCHAR(100) PRIMARY KEY COMMENT '设备唯一标识 (内部)',
    DeviceName VARCHAR(255) NOT NULL COMMENT '设备名称 (遵循E.2.5.4)',
    SubstationName VARCHAR(100) NOT NULL COMMENT '所亭名称 (E.2.1)',
    InstallArea VARCHAR(100) NOT NULL COMMENT '安装区域 (E.2.3)',
    LocationDesc VARCHAR(255) COMMENT '位置描述',
    DeviceCategory VARCHAR(50) NOT NULL COMMENT '设备大类 (E.2.5.4)',
    DeviceSubCategory VARCHAR(100) NOT NULL COMMENT '设备子类 (E.2.5.4)',
    Supplier VARCHAR(100) COMMENT '设备厂商',
    Model VARCHAR(100) COMMENT '设备型号',
    IPAddress VARCHAR(45) COMMENT 'IP地址 (若网络设备)',
    Port INT COMMENT '端口号',
    IsEnabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    ConfigParams JSON COMMENT '其他配置参数 (如通信参数, 厂家特定参数)',
    LastUpdateTime TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    Remarks TEXT COMMENT '备注'
);
        </code></pre>
<p><strong>示例：`CameraPresetConfig` (摄像机预置位配置表 - 简化)</strong></p>
<pre><code class="sql">
CREATE TABLE CameraPresetConfig (
    PresetID VARCHAR(100) PRIMARY KEY COMMENT '预置位唯一标识 (内部)',
    SubstationName VARCHAR(100) NOT NULL COMMENT '所亭名称 (E.2.5.9)',
    CameraName VARCHAR(255) NOT NULL COMMENT '摄像机名称 (E.2.5.9, 关联DeviceConfig)',
    PresetCode VARCHAR(50) NOT NULL COMMENT '预置位编号 (E.2.5.9)',
    PresetName VARCHAR(255) NOT NULL COMMENT '预置位名称 (E.2.5.9, 遵循E.2.4)',
    TargetDeviceName VARCHAR(255) COMMENT '目标设备名称 (E.2.5.9, 遵循E.2.1, 可空)',
    Pan INT COMMENT '水平转角',
    Tilt INT COMMENT '垂直转角',
    Zoom INT COMMENT '变倍值',
    Description TEXT COMMENT '描述',
    UNIQUE KEY idx_cam_preset_code (CameraName, PresetCode)
);
        </code></pre>
</line></line></line></line></line></line></line></line></text></line></path></text></text></path></path></text></text></text></path></text></path></text></path></text></path></text></line></line></line></line></line></line></line></line></line></line></rect></rect></text></text></text></text></div>
<script>
        // ECharts - System Composition Chart
        var systemCompositionChart = echarts.init(document.getElementById('systemCompositionChart'));
        var systemCompositionOption = {
            title: {
                text: '系统功能模块组成',
                subtext: '核心模块占比示意',
                left: 'center'
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b} : {c}%'
            },
            legend: {
                orient: 'vertical',
                left: 'left',
                data: ['视频监控与巡检', '智能识别', '安全防范与门禁', '环境监测', '火灾报警', '动力照明控制', '数据与接口服务', '系统管理与配置']
            },
            series: [
                {
                    name: '功能模块',
                    type: 'pie',
                    radius: '60%',
                    center: ['50%', '60%'],
                    data: [
                        {value: 25, name: '视频监控与巡检'},
                        {value: 15, name: '智能识别'},
                        {value: 10, name: '安全防范与门禁'},
                        {value: 10, name: '环境监测'},
                        {value: 10, name: '火灾报警'},
                        {value: 10, name: '动力照明控制'},
                        {value: 10, name: '数据与接口服务'},
                        {value: 10, name: '系统管理与配置'}
                    ],
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }
            ]
        };
        systemCompositionChart.setOption(systemCompositionOption);

        // ECharts - Availability Metrics Chart
        var availabilityMetricsChart = echarts.init(document.getElementById('availabilityMetricsChart'));
        var availabilityOption = {
            title: { text: '系统可用性指标对比', left: 'center' },
            tooltip: { trigger: 'axis', axisPointer: { type: 'shadow'} },
            legend: { data: ['标准要求', '设计目标'], top: 30 },
            grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
            xAxis: { type: 'value', boundaryGap: [0, 0.01] },
            yAxis: {
                type: 'category',
                data: ['系统可用率 (%)', 'MTBF (h)', 'MTTR (min)', 'RTO (min)', 'RPO (min)']
            },
            series: [
                {
                    name: '标准要求',
                    type: 'bar',
                    data: [99.99, 30000, 30, 30, 15]
                },
                {
                    name: '设计目标',
                    type: 'bar',
                    data: [99.99, 35000, 15, 15, 5]
                }
            ]
        };
        availabilityMetricsChart.setOption(availabilityOption);

        // ECharts - Realtime Metrics Chart
        var realtimeMetricsChart = echarts.init(document.getElementById('realtimeMetricsChart'));
        var realtimeOption = {
            title: { text: '实时性指标对比 (单位: ms)', subtext:'指标越小越好', left: 'center' },
            tooltip: { trigger: 'axis', axisPointer: { type: 'shadow'} },
            legend: { data: ['标准要求 (ms)', '设计目标 (ms)'], top: 40 },
            grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
            xAxis: { type: 'value', boundaryGap: [0, 0.01], axisLabel: {formatter: '{value} ms'} },
            yAxis: {
                type: 'category',
                data: ['视频切换响应', '控制命令响应', '画面显示延迟', '事件报警响应', 'SOE分辨率']
            },
            series: [
                {
                    name: '标准要求 (ms)',
                    type: 'bar',
                    data: [1000, 1000, 1000, 1000, 2]
                },
                {
                    name: '设计目标 (ms)',
                    type: 'bar',
                    data: [500, 500, 500, 300, 1]
                }
            ]
        };
        realtimeMetricsChart.setOption(realtimeOption);

        // KPI Monitoring Chart
        var kpiChart = echarts.init(document.getElementById('kpiMonitoringChart'));
        var kpiOption = {
            title: { text: '关键性能指标 (KPI) 监控阈值示例', left: 'center' },
            tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
            legend: { data: ['正常值上限', '告警阈值'], top: 30 },
            grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
            xAxis: {
                type: 'category',
                data: ['CPU使用率(%)', '内存使用率(%)', '磁盘使用率(%)', '网络带宽(%)', '服务响应(ms)']
            },
            yAxis: { type: 'value' },
            series: [
                {
                    name: '正常值上限',
                    type: 'bar',
                    data: [70, 80, 80, 70, 1000],
                    itemStyle: { color: '#91cc75' }
                },
                {
                    name: '告警阈值',
                    type: 'bar',
                    data: [85, 90, 90, 85, 2000],
                    itemStyle: { color: '#fac858' }
                }
            ]
        };
        kpiChart.setOption(kpiOption);

        // Resize charts on window resize
        window.addEventListener('resize', function() {
            systemCompositionChart.resize();
            availabilityMetricsChart.resize();
            realtimeMetricsChart.resize();
            kpiChart.resize();
        });

    </script>
</div></body>
</html>