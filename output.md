系统模块

后台服务: asp.net web api +Vue

数据服务 wpf

巡检服务 qt C++

客户端 qt C++

识别服务 C++

后台服务作为支撑，连接数据平台，为客户端提供配置数据接口，数据上传接口；并提供对外接口，与辅助监控主站进行通信；

数据服务连接通信管理机，负责实时数据的接受与转发；数据服务还需要将信号转为104格式发送到主站；（所内数据服务为服务端，主站104为客户端）并处理定时遥控，信号联动等操作（遥控信号直接下发到通信管理机，摄像头预置位联动发送到巡检服务执行），数据通过消息中间件转发给客户端，同时存储历史数据；

巡检服务连接NVR与摄像头，执行视频联动，视频巡检，红外摄像头测温等任务，从后台服务获取配数据，上传结果数据到后台服务；


重要数据流向：
1.联动数据    
2.巡检数据
3.报警信息  报警主要有遥信变位，遥测越限两种类型


数据库

配置库：mysql

历史库：mongodb

实时库：redis

消息中间件：redis

操作系统win10
