# 辅助监控系统数据流向图表

## 1. 联动数据流向

### 1.1 视频联动流向图
```mermaid
flowchart TD
    A[报警事件] --> B[数据服务]
    B --> C[Redis消息队列]
    C --> D[巡检服务]
    D --> E[摄像头控制]
    
    B --> F[后台服务]
    F --> G[Redis消息队列]
    G --> H[客户端显示]
    
    style A fill:#ff9999
    style E fill:#99ff99
    style H fill:#9999ff
```

### 1.2 遥控联动流向图
```mermaid
flowchart TD
    A[报警事件] --> B[数据服务]
    B --> C[通信管理机]
    C --> D[执行设备]
    
    B --> E[后台服务]
    E --> F[Redis消息队列]
    F --> G[客户端显示]
    
    style A fill:#ff9999
    style D fill:#99ff99
    style G fill:#9999ff
```

## 2. 巡检数据流向图

```mermaid
flowchart TD
    A[巡检任务配置] --> B[后台服务]
    B --> C[巡检服务]
    C --> D[执行巡检]
    
    E[摄像头/NVR] --> F[图像数据]
    F --> C
    C --> G[识别服务]
    G --> H[识别结果]
    H --> C
    
    C --> I[巡检结果]
    I --> B
    B --> J[存储/显示]
    B --> K[主站系统]
    
    style A fill:#ffcc99
    style G fill:#cc99ff
    style K fill:#99ffcc
```

## 3. 报警信息流向图

```mermaid
flowchart TD
    A[现场设备] --> B[通信管理机]
    B --> C[数据服务]
    C --> D[Redis实时库]
    
    D --> E[报警判断逻辑]
    E --> F{报警类型}
    F -->|遥信变位| G[开关量状态变化]
    F -->|遥测越限| H[模拟量超阈值]
    
    E --> I[Redis消息队列]
    I --> J[客户端实时显示]
    
    E --> K[后台服务存储]
    K --> L[MongoDB历史库]
    
    style A fill:#ff9999
    style F fill:#ffff99
    style J fill:#9999ff
    style L fill:#99ff99
```

## 4. 数据库架构与数据流

```mermaid
flowchart TD
    subgraph "配置库 MySQL"
        A1[设备配置]
        A2[测点配置]
        A3[用户权限]
        A4[系统参数]
    end
    
    subgraph "实时库 Redis"
        B1[实时测点数据]
        B2[设备状态]
        B3[会话信息]
        B4[消息队列]
    end
    
    subgraph "历史库 MongoDB"
        C1[历史数据]
        C2[操作记录]
        C3[报警记录]
        C4[巡检记录]
    end
    
    D[后台服务] <--> A1
    E[数据服务] --> B1
    F[各服务] --> D
    D --> C1
    
    G[客户端] <--> B1
    H[其他服务] --> A1
    
    style D fill:#ffcc99
    style E fill:#cc99ff
    style G fill:#9999ff
```

## 5. 关键数据处理流程

### 5.1 实时数据处理流程
```mermaid
flowchart TD
    A[现场设备] --> B[通信管理机]
    B --> C[数据服务<br/>协议解析]
    C --> D[Redis实时缓存]
    
    D --> E[数据校验]
    E --> F[报警判断/联动控制]
    F --> G[Redis消息队列]
    G --> H[各订阅服务<br/>客户端]
    
    F --> I[历史数据存储<br/>MongoDB]
    
    style A fill:#ff9999
    style C fill:#ffcc99
    style D fill:#99ccff
    style H fill:#9999ff
    style I fill:#99ff99
```

### 5.2 遥控命令处理流程
```mermaid
flowchart TD
    A[主站/客户端] --> B[后台服务<br/>权限验证]
    B --> C[数据服务<br/>命令转换]
    C --> D[通信管理机]
    D --> E[现场设备]
    
    E --> F[执行结果反馈]
    F --> D
    D --> C
    C --> G[Redis消息队列]
    G --> H[客户端显示]
    
    style A fill:#9999ff
    style B fill:#ffcc99
    style E fill:#ff9999
    style H fill:#99ff99
```

## 6. 完整系统架构数据流

```mermaid
graph TB
    subgraph "现场层"
        A[现场设备]
        B[摄像头/NVR]
    end
    
    subgraph "通信层"
        C[通信管理机]
    end
    
    subgraph "服务层"
        D[数据服务]
        E[后台服务]
        F[巡检服务]
        G[识别服务]
    end
    
    subgraph "数据层"
        H[Redis实时库]
        I[MySQL配置库]
        J[MongoDB历史库]
        K[Redis消息队列]
    end
    
    subgraph "应用层"
        L[客户端]
        M[主站系统]
    end
    
    A --> C
    B --> F
    C --> D
    D --> H
    D --> K
    F --> G
    G --> F
    F --> E
    E --> I
    E --> J
    E --> K
    K --> L
    E --> M
    L --> E
    
    style A fill:#ff9999
    style D fill:#ffcc99
    style E fill:#cc99ff
    style H fill:#99ccff
    style L fill:#9999ff
```

## 说明

### 图表说明：
- **红色节点**：现场设备/输入源
- **橙色节点**：核心服务组件
- **紫色节点**：处理服务
- **蓝色节点**：数据存储
- **绿色节点**：输出/显示

### 数据流特点：
1. **实时性**：Redis提供毫秒级响应
2. **可靠性**：多重数据备份和存储
3. **扩展性**：微服务架构支持水平扩展
4. **一致性**：通过消息队列保证数据同步
