E.2　基础数据互提规范
E.2.1　供电设备命名规范
牵引变电所辅助监控系统厂商向辅助监控系统主站厂商提供相应点表时，供电设备的命名应遵循如表E.1 定义的命名规范，以确保二者对供电设备描述的一致性。
表E.1	供电设备命名规范
名称	填写要求	样例
序号	作统计数量用，以1、2、3…顺编。	1
线路名称	必填	京沪高铁
所亭名称	必填	XX变电所
设备类型名称	必填
设备类型列表如下：
牵引变压器，所用变压器、AT变压器，断路器，隔离开关，负荷开关，GIS开关柜，AIS开关柜，电压互感器，电流互感器，补偿装置，母线，避雷器，避雷针，抗雷圈，放电装置，接地装置，交流盘，直流电源装置，综合自动化系统，端子箱，电缆，光缆。
其他设备类型名称参见铁路牵引供电设备设施单元划分、编码暂行规范中基本单元代码。	断路器
牵引变压器
运行编号	必填	101
1B
设备名称	必填
命名规则：运行编号_设备类型名称。	101_断路器
1B_牵引变压器
E.2.2　摄像机图像显示规范
辅助监控系统的摄像机画面，需按规范在视频画面上显示当前时间、摄像机名称和预置位名称，摄像机图像显示应遵循如表E.2定义的命名规范。


表E.2	摄像机图像显示规范
名称	显示要求
当前时间	位置： 画面左上角
格式：2019-08-21 20:00:00
摄像机名称	位置： 画面右上角
格式：参见摄像机预置位点表中的摄像机名称
预置位名称	位置： 画面右下角
格式：参见摄像机预置位点表中的预置位名称
所亭名称	位置： 画面左下角
格式：XX变电所/AT所/分区所/配电所
E.2.3　辅助监控设备安装区域命名规范
辅助监控设备安装区域的名称应采用如表F.2的规范命名。
表E.3	辅助监控设备安装区域的名称规范
序号	安装区域名称
1	进线区
2	主变区
3	进线高压室
4	27.5kV高压室
5	馈线区
6	所用变室
7	控制室
8	电缆夹层
9	通信机械室
10	屋顶
11	周界围墙
12	其他


E.2.4　摄像机拍摄焦点命名规范
摄像机拍摄焦点是指摄像头聚焦的一次设备的关键部件、仪表等设备细节或全景的名称。摄像机拍摄焦点的命名应遵循如表E.4定义的命名规范。







表E.4	摄像机拍摄焦点命名规范
序号	供电设备	供电设备应关注的焦点类别	拍摄焦点名称（举例）
1	断路器	全景
指示
表计
触指	全景
分合闸指示
弹簧储能指示
气体压力表计
上触指
下触指
2	电动隔离开关	全景
进线线夹
出线线夹
连接点	全景
A相全景
A相进线线夹
A相出线线夹
B相全景
B相进线线夹
B相出线线夹
C相全景
C相进线线夹
C相出线线夹
进线连接点
出线连接点
3	电流互感器
电压互感器
避雷器	全景
连接点
表计	全景
A相进线连接点
A相出线连接点
B相进线连接点
B相出线连接点
C相进线连接点
C相出线连接点
A相表计
B相表计
C相表计













表E.4（续）
序号	供电设备	供电设备应关注的焦点类别	拍摄焦点名称（举例）
4	变压器	全景
油位计
连接点
母排
调压器
表计	全景
油位计
A相进线连接点
A相出线连接点
B相进线连接点
B相出线连接点
C相进线连接点
C相集中母排
调压器
温控器表计
5	避雷器	全景
连接点
表计	全景
进线连接点
表计
6	熔断器	全景
端头	全景
上端头
下端头
7	母排	连接点	连接点
8	手动隔离开关
地刀
T型线夹
并勾线夹
耐张线夹
瓷瓶
绝缘子
穿墙套管
风机
辅助监控屏
通信屏	全景	全景

E.2.5　辅助监控系统厂家提供的基础数据规范
E.2.5.1　基本要求 
辅助监控系统厂家提供的基础数据应满足以下要求：
a)	辅助监控系统厂商向辅助监控系统主站厂商提供的基础数据包括：
1)	牵引变电所基本情况表；
2)	视频服务器配置信息表；
3)	辅助监控设备配置表；
4)	辅助监控遥控点表；
5)	辅助监控遥信点表；
6)	辅助监控遥测点表；
7)	辅助监控遥调点表；
8)	摄像机预置位点表。
b)	辅助监控系统厂商采用excel表格提供基础数据，表格中的字段名称和填写规范如表E.5-E.18所示；
c)	工程实施完成后，当辅助监控系统的基础数据（包括辅助监控设备配置表、辅助监控遥控点表、遥信点表、遥测点表、遥调点表和摄像机预置位点表）有更新时，段级或局级辅助监控区可通过辅助监控系统提供的接口召唤点表。
E.2.5.2　牵引变电所基本情况表
牵引变电所基本情况表格式示例如图E.1所示。

 
图E.1	牵引变电所基本情况表格式示例图

牵引变电所基本情况表填写规范如表E.5所示。
表E.5	牵引变电所基本情况表填写规范
字段名称	填写要求
序号	作统计数量用，以1、2、3…顺编。
线路名称	必填
所亭名称	必填
辅助监控设备布置图	必填，单独提供dwg或dwf格式的文件，此列填写文件名。
所亭坐标	必填，格式：纬度,经度。
HTTP服务的地址	必填，变电所辅助监控HTTP服务的IP端口，格式：IP:端口。
E.2.5.3　视频服务器配置信息表
视频服务器配置信息表格式示例如图E.2所示。若同一视频服务器提供多个IP地址和端口，则填写多行。

 
图E.2	视频服务器配置信息表格式示例图

视频服务器配置信息表填写规范如表E.6所示。
表E.6	视频服务器配置信息表填写规范
字段名称	填写要求
序号	作统计数量用，以1、2、3…顺编。
所亭名称	必填，与变配电所基本情况表的所亭名称一致。
IP地址及端口号	必填，接入铁路供电调度控制系统主站网络的IP地址、端口号。
用户名	必填
密码	必填
备注	非必填
E.2.5.4　辅助监控设备配置表
辅助监控设备分为视频设备和非视频设备，视频设备配置表格式示例如图E.3所示。

 
图E.3	视频设备配置表格式示例图

视频设备配置表填写规范如表E.7所示。
表E.7	视频设备配置表填写规范
字段名称	填写要求
序号	作统计数量用，以1、2、3…顺编。
所亭名称	必填，与变配电所基本情况表的所亭名称一致。
安装区域	必填，名称与辅助监控设备安装区域命名规范中的名称一致。
位置描述	必填，如设备的安装位置（方位）或本区域内的编号，本区域内不重复。
设备大类	必填，名称列表如下：
视频,音频。
设备子类	必填，名称列表如下：
视频：枪机,云台枪机,球机,半球机,热成像枪机, 热成像云台枪机,热成像球机,导轨式巡检摄像机。
音频： 拾音器。

双光谱摄像机通道	非必填，仅双光谱摄像机填写，填写A或B。
A：表示可见光，B：表示热成像。
设备名称	必填，自动生成。
命名规则：安装区域_位置描述_设备子类+双光谱摄像机通道。

非视频设备配置表格式示例如图E.4所示。

 
图E.4	非视频设备配置表格式示例图

非视频设备配置表填写规范如表E.8所示。



表E.8	非视频设备配置表填写规范
字段名称	填写要求
序号	作统计数量用，以1、2、3…顺编。
所亭名称	必填，与变配电所基本情况表的所亭名称一致。
安装区域	必填，名称与设备安装区域命名规范中的名称一致。
位置描述	必填，如设备的安装位置（方位）或本区域内的编号，本区域内不重复。
设备大类	必填，名称列表如下：
安防,环境监测,消防,动力照明。
设备子类	必填，名称列表如下：
安防：门禁,红外双鉴,红外三鉴,红外对射,激光对射,玻璃破碎,电子围栏。
环境监测：水浸传感器,温湿度传感器,微气象站,SF6监测传感器。
消防：气灭装置,烟感,温感,手动报警按钮,声光报警器。
动力照明：风机,水泵,空调,照明。
设备名称	必填，自动生成。
命名规则：安装区域_位置描述_设备子类。

段级辅助监控区可通过辅助监控系统提供的接口召唤辅助监控设备配置表，处理流程如图E.5所示。
 
图E.5	召唤辅助监控设备配置表接口图
召唤辅助监控设备配置表接口应满足以下要求：
a)	数据方向：辅助监控系统  ->  段级辅助监控区；
b)	接口提供方：辅助监控系统；
c)	接口说明：采用HTTPS协议，POST方式提交；
d)	接口路径：https://ip:port/项目路径/api/callDeviceList；
e)	数据格式：数据交换格式，body采用JSON字符串，header 需包含token字符串,字符串的内容参考基础数据字段；
f)	有关返回数据结构如图E.6、返回数据说明如表E.9所示：

 
图E.6	返回数据结构图

表E.9	返回数据说明
字段	名称	数据类型	描述
Key1	值1	String	
Key2	值2	String	
E.2.5.5　辅助监控遥控点表
辅助监控遥控点表格式示例如图E.7所示。
 
图E.7	辅助监控遥控点表格式示例图

辅助监控遥控点表填写规范如表E.10所示。
表E.10	辅助监控遥控点表填写规范
字段名称	填写要求
序号	作统计数量用，以1、2、3…顺编。
所亭名称	必填，与变配电所基本情况表的所亭名称一致。
设备名称	必填，与辅助监控设备配置表的设备名称一致。
点位名称	必填
“单点对象状态=0”
或“双点对象状态=1” 
的遥控状态描述	必填
“单点对象状态=1”
或“双点对象状态=2”
的遥控状态描述	必填
遥控地址编码	依据附录I的信息对象地址范围，为了保证遥控地址编码全所唯一且与综自系统的遥控地址编码段进行区分。
辅助监控系统遥控地址编码采用十进制编码，起始地址编码定义为：24901。
段级辅助监控区可通过辅助监控系统提供的接口召唤辅助监控遥控点表，处理流程如图E.8所示。
 
图E.8	召唤辅助监控遥控点表接口图
召唤辅助监控遥控点表接口应满足以下要求：
a)	数据方向：辅助监控系统  ->  段级辅助监控区；
b)	接口提供方：辅助监控系统；
c)	接口路径：https://ip:port/项目路径/api/callRemoteControlList；
d)	接口说明：采用HTTPS协议，POST方式提交；
e)	数据格式：数据交换格式，body采用JSON字符串，header 需包含token字符串。字符串的内容参考基础数据字段；
f)	有关返回数据结构如图E.9、返回数据说明如表E.11所示。
 
图E.9	返回数据结构图

表E.11	返回数据说明
字段	名称	数据类型	描述
Key1	值1	String	
Key2	值2	String	
E.2.5.6　辅助监控遥信点表
辅助监控遥信点表格式示例如图E.10所示。
 
图E.10	辅助监控遥信点表格式示例图

辅助监控遥信点表填写规范如表E.12所示。



表E.12	辅助监控遥信点表填写规范
字段名称	填写要求
序号	作统计数量用，以1、2、3…顺编。
所亭名称	必填，与变配电所基本情况表的所亭名称一致。
设备名称	必填，与辅助监控设备配置表的设备名称一致。
点位名称	必填
“单点对象状态=0”
或“双点对象状态=1”
的遥信状态描述	必填
“单点对象状态=1”
或“双点对象状态=2” 
的遥信状态描述	必填
遥信地址编码	依据附录I的信息对象地址范围，为了保证遥信地址编码全所唯一且与综自系统的遥信地址编码段进行区分。
辅助监控系统遥信地址编码采用十进制编码，起始地址编码定义为：3001。
段级辅助监控区通过辅助监控系统提供的接口召唤辅助监控遥信点表，处理流程如图E.11所示。
 
图E.11	召唤辅助监控遥信点表接口图
召唤辅助监控遥信点表接口应满足以下要求：
a)	数据方向：辅助监控系统  ->  段级辅助监控区；
b)	接口提供方：辅助监控系统；
c)	接口说明：采用HTTPS协议，POST方式提交；
d)	接口路径：https://ip:port/项目路径/api/callRemoteSignalList；
e)	数据格式：数据交换格式，body采用JSON字符串，header 需包含token字符串。字符串的内容参考基础数据字段；
f)	有关返回数据结构如图E.12、返回数据说明如表E.13所示。
 
图E.12	返回数据结构图

表E.13	返回数据说明
字段	名称	数据类型	描述
Key1	值1	String	
Key2	值2	String	
E.2.5.7　辅助监控遥测点表
辅助监控遥测点表格式示例如图E.13所示。
 
图E.13	辅助监控遥测点表格式示例图
辅助监控遥测点表填写规范如表E.14所示。

表E.14	辅助监控遥测点表填写规范
字段名称	填写要求
序号	作统计数量用，以1、2、3…顺编。
所亭名称	必填，与变配电所基本情况表的所亭名称一致。
设备名称	必填，与辅助监控设备配置表的设备名称一致。
点位名称	必填
单位	必填
遥测地址编码	依据附录I的信息对象地址范围，为了保证遥测地址编码全所唯一且与综自系统的遥测地址编码段进行区分。
辅助监控系统遥测地址编码采用十进制编码，起始地址编码定义为：20001。
段级辅助监控区通过辅助监控系统提供的接口召唤辅助监控遥测点表，处理流程如图E.14所示。
 
图E.14	召唤辅助监控遥测点表接口图
召唤辅助监控遥测表接口应满足以下要求：
a)	数据方向：辅助监控系统  ->  段级辅助监控区；
b)	接口提供方：辅助监控系统；
c)	接口路径：https://ip:port/项目路径/api/callRemoteMeterList；
d)	接口说明：采用HTTPS协议，POST方式提交；
e)	数据格式：数据交换格式，body采用JSON字符串，header 需包含token字符串。字符串的内容参考基础数据字段；
f)	有关返回数据结构如图E.15、返回数据说明如表E.15所示。
 
图E.15	返回数据结构图

表E.15	返回数据说明
字段	名称	数据类型	描述
Key1	值1	String	
Key2	值2	String	
E.2.5.8　辅助监控遥调点表
辅助监控遥调点表格式示例如图E.16所示。
 
图E.16	辅助监控遥调点表格式示例图

辅助监控遥调点表填写规范如表E.16所示。


表E.16	辅助监控遥调点表填写规范
字段名称	填写要求
序号	作统计数量用，以1、2、3…顺编。
所亭名称	必填，与变配电所基本情况表的所亭名称一致。
设备名称	必填，与辅助监控设备配置表的设备名称一致。
点位名称	必填
操作类型	1 升降档 2设定目标值
“单点对象状态=0”
或“双点对象状态=1” 
的遥调状态描述	非必填，升降档必填，设定目标值不填
“单点对象状态=1”
或“双点对象状态=2”
的遥调状态描述	非必填，升降档必填，设定目标值不填
遥调地址编码	依据附录I的信息对象地址范围，为了保证遥调地址编码全所唯一且与综自系统的遥调地址编码段进行区分。
辅助监控系统遥调地址编码采用十进制编码，起始地址编码定义为：25001。
段级辅助监控区通过辅助监控系统提供的接口召唤辅助监控遥调点表，处理流程如图E.17所示。
 
图E.17	召唤辅助监控遥调点表接口图
召唤辅助监控遥调表接口应满足以下要求：
a)	数据方向：辅助监控系统  ->  段级辅助监控区；
b)	接口提供方：辅助监控系统；
c)	接口路径：https://ip:port/项目路径/api/callRemoteAdjustList；
d)	接口说明：采用HTTPS协议，POST方式提交；
e)	数据格式：数据交换格式，body采用JSON字符串，header 需包含token字符串。字符串的内容参考基础数据字段；
f)	有关返回数据结构如图E.18、返回数据说明如表E.17所示。
 
图E.18	返回数据结构图

表E.17	返回数据说明
字段	名称	数据类型	描述
Key1	值1	String	
Key2	值2	String	
E.2.5.9　摄像机预置位点表
摄像机预置位点表格式示例如图E.19所示，摄像机预置位点表包含固定摄像机、导轨式巡检摄像机的预置位点表。
 
图E.19	摄像机预置位点表格式示例图

摄像机预置位点表填写规范如表E.18所示。






表E.18	摄像机预置位点表填写规范
字段名称	填写要求
序号	作统计数量用，以1、2、3…顺编。
所亭名称	必填，与变配电所基本情况表的所亭名称一致。
目标设备名称	当预置位拍摄单个设备时，填供电设备名称或辅助设备名称；当拍摄多个设备（如全景）时不填。
摄像机名称	必填，取值为“辅助监控设备配置表”中的“设备名称”。
预置位编号	必填，数字1-65535。
固定式枪机：不填。
预置位名称	必填，命名规则：供电设备运行编号-拍摄焦点中文名称。
拍摄焦点中文名称与摄像机拍摄焦点命名规范中的名称一致。
段级辅助监控区通过辅助监控系统提供的接口召唤预置位点表，处理流程如图E.20所示。
 
图E.20	召唤摄像机预置位点表接口图
a)	数据方向：辅助监控系统  ->  段级辅助监控区；
b)	接口提供方：辅助监控系统；
c)	接口路径：https://ip:port/项目路径/api/callCameraPresetList；
d)	接口说明：采用HTTPS协议，POST方式提交；
e)	数据格式：数据交换格式，body采用JSON字符串，header 需包含token字符串。字符串的内容参考基础数据字段；
f)	有关返回数据结构如图E.21、返回数据说明如表E.19所示。
 
图E.21	返回数据结构图
表E.19	返回数据说明
字段	名称	数据类型	描述
Key1	值1	String	
Key2	值2	String	
E.3　数据采集及远方控制接口
E.3.1　接口范围及内容
辅助监控系统应向段级辅助监控区上报照明、风机、水泵、空调、门禁、在线监测等所有辅助设备的设备状态和报警信息；应向局级辅助监控区上报火灾、SF6等辅助设备的重要报警信息，段级辅助监控区可远方控制辅助监控系统的辅助设备。
E.3.2　段级辅助监控区和局级辅助监控区与辅助监控系统的数据采集接口
辅助监控系统将所内安防、环境监控、消防等辅助监控设备的设备状态实时上送段级辅助监控区，包括遥信（含报警信息）和遥测（含遥调档位）。辅助监控系统将辅助设备的全部报警信息（含红外测温的报警、图像识别发现的缺陷预警）实时上送段级辅助监控区，将重要的报警信息（如火灾报警、SF6泄露等）实时上送局级辅助监控区。处理流程如图E.22、图E.23所示。
 
图E.22	数据采集接口图
 
图E.23	上送报警信息接口图
上送报警信息接口应满足以下要求：
a)	数据方向：辅助监控系统  ->  段级辅助监控区；
b)	接口提供方：段级辅助监控区；
c)	接口说明：采用Q/CR 796-2020附录I的104传输规约。
E.3.3　段级辅助监控区与辅助监控系统的远方控制接口
段级辅助监控区按需对辅助监控系统的照明、风机、水泵、空调、门禁等辅助监控设备进行远方控制（遥控和遥调），处理流程如图E.24所示。
 
图E.24	远方控制接口图
设备远方控制接口应满足以下要求：
a)	数据方向：段级辅助监控区  ->  辅助监控系统；
b)	接口提供方：辅助监控系统；
c)	接口说明：采用Q/CR 796-2020附录I的104传输规约。
E.4　视频调阅及控制接口
E.4.1　接口范围
段级辅助监控区、局级辅助监控区与辅助监控系统间均应实现视频调阅及控制接口。
E.4.2　调阅视频接口
调阅视频接口应满足以下要求：
a)	辅助监控系统的所有摄像机（含固定摄像机和移动巡检摄像机）接入所内NVR，所内NVR接入段级辅助监控区和局级辅助监控区，段级辅助监控区和局级辅助监控区通过NVR获取所有摄像机的实时视频和录像，实时视频可切换主码流和子码流（子码流不高于720P，主码流不低于720P）；
b)	段级辅助监控区和局级辅助监控区通过NVR调用固定摄像机的预置位，通过辅助监控系统提供的https接口调用移动巡检设备的调用预置位；
c)	接口标准：视频设备联网需遵循GB/T 28181的规定或采用ONVIF协议。
E.4.3　调用移动巡检设备预置位的接口
辅助监控系统的移动巡检设备包括导轨式巡检摄像机，段级辅助监控区和局级辅助监控区通过辅助监控系统提供的接口调用移动巡检设备的摄像机预置位（移动巡检设备本体的位置和摄像机的预置位），处理流程如图E.25所示。
 
图E.25	调用移动巡检设备的预置位接口图
调用移动巡检设备预置位的接口应满足以下要求：
a)	数据方向：铁路供电调度控制系统主站  ->  辅助监控系统；
b)	接口提供方：辅助监控系统；
c)	接口路径：https://ip:port/项目路径/api/callPreset；
d)	接口说明：采用HTTPS协议，POST方式提交；
e)	数据格式：数据交换格式，body采用JSON字符串，header 需包含token字符串；
f)	有关请求数据结构如图E.26、请求数据说明如表E.20、返回数据结构如图E.27、返回数据说明如表E.21所示。
 
图E.26	返回数据结构图

表E.20	请求数据说明
字段	名称	数据类型	描述
subName	所亭名称	String	如：XX变电所
camName	设备名称	String	如：高压室_顶部_导轨
presetCode	预置位编号	String	如：2

 
图E.27	返回数据结构图


表E.21	返回数据说明
字段	名称	数据类型	描述
status	状态	int	0成功 1失败
message	返回信息	String	对status的描述

E.4.4　获取移动巡检设备当前位置的接口
段级辅助监控区和局级辅助监控区通过辅助监控系统提供的接口获取移动巡检设备的当前位置，处理流程如图E.28所示。
 
图E.28	获取移动巡检设备的当前位置接口图
获取移动巡检设备的当前位置接口应满足以下要求：
a)	数据方向：铁路供电调度控制系统主站  ->  辅助监控系统；
b)	接口提供方：辅助监控系统；
c)	接口路径：https://ip:port/项目路径/api/getCurrentPosition；
d)	接口说明：采用HTTPS协议，POST方式提交；
e)	数据格式：数据交换格式，body采用JSON字符串，header 需包含token字符串；
f)	坐标说明：设备的坐标原点（0，0，0）以设备自身的坐标系为准，例如导轨式巡检摄像机以初始化的端点为原点。坐标系为三维直角坐标系，且X、Y轴应在水平方向，Z轴垂直向上。坐标系以米为单位，精度保留到小数点后两位（厘米）。
g)	有关请求数据结构如图E.29、请求数据说明如表E.22、返回数据结构如图E.30、返回数据说明及位置信息data分别如表E.23~表E.24所示。
 
图E.29	请求数据结构图
表E.22	请求数据说明
字段	名称	数据类型	描述
subName	所亭名称	String	如：XX变电所
camName	设备名称	String	如：高压室_顶部_导轨
 
图E.30	返回数据结构图
表E.23	返回数据说明
字段	名称	数据类型	描述
status	状态	int	0成功 1失败
message	返回信息	String	对status的描述
表E.24	位置信息data
字段	名称	数据类型	描述
x	当前x坐标值	double	当前区域坐标位置x，如33.30
y	当前y坐标值	double	当前区域坐标位置y，如23.31
z	当前z坐标值	double	当前区域坐标位置y，如4.81
time	获取位置的时间	String	如2019-01-01 12:02:35
E.4.5　设置摄像机的预置位
段级辅助监控区支持设置摄像机的预置位，包括修改预置位（调整已存在预置位的云台、变焦）、新增预置位，通过辅助监控系统提供的接口通知辅助监控系统保存预置位。处理流程如图E.31所示。
 
图E.31	新增/修改摄像机的预置位
设置摄像机的预置位接口应满足以下要求：
a)	数据方向：辅助监控系统段级主站  ->  辅助监控系统；
b)	接口提供方：辅助监控系统；
c)	接口路径：https://ip:port/项目路径/api/savePreset；
d)	接口说明：采用HTTPS协议，POST方式提交；
e)	数据格式：数据交换格式，body采用JSON字符串，header 需包含token字符串；
f)	有关请求数据结构如图E.32、请求数据说明如表E.25、返回数据结构如图E.33、返回数据说明表E.26所示。
 
图E.32	请求数据结构
表E.25	请求数据说明
字段	名称	数据类型	描述
subName	所亭名称	String	如：XX变电所
equipName	目标设备名称	String	如：1021_隔离开关
camName	摄像机名称	String	如：进线区_东侧龙门柱_球机
presetName	预置位名称	String	如：1021-B相出线线夹
presetCode	预置位编号	String	如：23


 
图E.33	返回数据结构图
表E.26	返回数据说明

字段	名称	数据类型	描述
status	状态	int	0成功 1失败
message	返回信息	String	对status的描述
E.5　视频巡检接口
E.5.1　接口范围
段级辅助监控区与辅助监控系统间应实现视频巡检接口。
E.5.2　视频巡检交互流程
段级辅助监控区与辅助监控系统之间的视频巡检交互流程如图E.34所示。
 
图E.34	视频巡检交互流程图
E.5.3　辅助监控局级主站/段级主站与辅助监控系统间的视频巡检详细接口
E.5.3.1　下发巡检计划
辅助监控局级主站/段级主站编制固定摄像机和移动巡检设备的巡检计划，按需将巡检计划下发给辅助监控系统，辅助监控系统应支持多任务并行巡检，处理流程如图E.35所示。
 
图E.35	下发巡检计划接口图
下发巡检计划接口应满足以下要求：
a)	数据方向：段级辅助监控系统主站  ->  辅助监控系统；
b)	接口提供方：辅助监控系统；
c)	接口路径：https://ip:port/项目路径/api/downloadPlan；
d)	接口说明：采用HTTPS协议，POST方式提交；
e)	数据格式：数据交换格式，body采用JSON字符串，header 需包含token字符串；
f)	有关请求数据结构如图E.36、巡检计划信息、周期信息、巡检点位信息如表E.27~表E.29、返回数据结构如图E.37、返回数据说明如表E.30所示。
 
图E.36	请求数据结构



表E.27	巡检计划信息
字段	名称	数据类型	描述
name	巡检计划名称	String	如：日常巡检
code	巡检计划编号	String	以此作为巡检计划的唯一标识
subName	所亭名称	String	如：XX变电所
centerType	主站类型	String	DUAN：段级主站
JU：局级主站
residenceTime	停留时间	int	如：5，单位秒，拍照前预置位停留时间，不应小于系统要求的最短时间（5秒）。




表E.28	周期信息
字段	名称	数据类型	描述
weeks	周期	String	周一至周日或无，周一至周日用1-7表示，逗号分隔
startDate	启动日期	String	当周期为无时，填写一次性巡检计划的启动日期，格式：yyyy-MM-dd
startTime	启动时间	String	时：分，如11：30

表E.29	巡检点位信息
字段	名称	数据类型	描述
seq	巡检序号	String	如：1、2、3
camName	摄像机名称	String	如：进线区_东侧龙门柱_球机
presetCode	预置位编号	String	如：2
pictureNum	拍照张数	int	如：1
residenceTime	停留时间	int	如：5，单位秒，拍照前预置位停留时间，不应小于系统要求的最短时间（5秒）。
注：巡检点位信息内容为空时表示删除这个计划。优先采用巡检点位中的停留时间，若巡检点位的停留时间为空，则以巡检计划中的参数为准，若巡检计划和巡检点位中的停留时间均为空，则以辅助监控系统中的参数为准。

 
图E.37	返回数据结构图
表E.30	返回数据说明
字段	名称	数据类型	描述
status	状态	int	0成功 1失败
message	返回信息	String	对status的描述

E.5.3.2　下发临时巡检任务
局级/段级辅助监控系统主站按需将立即执行的临时巡检任务下发给辅助监控系统，辅助监控系统应支持多任务并行巡检，处理流程如图E.38所示。
 
图E.38	下发临时巡检任务接口图
下发临时巡检任务接口应满足以下要求：
a)	数据方向：局级/段级辅助监控系统主站  ->  辅助监控系统；
b)	接口提供方：辅助监控系统；
c)	接口路径：https://ip:port/项目路径/api/downloadTempTask；
d)	接口说明：采用HTTPS协议，POST方式提交；
e)	数据格式：数据交换格式，body采用JSON字符串，header 需包含token字符串；
f)	有关请求数据结构如图E.39、巡检任务信息、巡检点位信息如表E.31~表E.32、返回数据结构如图E.40、返回数据说明如表E.33所示。
 
图E.39	请求数据结构图





表E.31	巡检任务信息
字段	名称	数据类型	描述
name	巡检任务名称	String	如：日常巡检
code	巡检任务编号	String	以此作为巡检任务的唯一标识
subName	所亭名称	String	如：XX变电所
centerType	主站类型	String	DUAN：段级主站
JU：局级主站
residenceTime	停留时间	int	如：5，单位秒，拍照前预置位停留时间，不应小于系统要求的最短时间（5秒）。
表E.32	巡检点位信息
字段	名称	数据类型	描述
seq	巡检序号	String	如：1、2、3
camName	摄像机名称	String	如：进线区_东侧龙门柱_球机
presetCode	预置位编号	String	如：2
pictureNum	拍照张数	int	如：1
residenceTime	停留时间	int	如：5，单位秒，拍照前预置位停留时间，不应小于系统要求的最短时间（5秒）。
注：优先采用巡检点位中的停留时间，若巡检点位的停留时间为空，则以巡检任务中的参数为准，若巡检任务和巡检点位中的停留时间均为空，则以辅助监控系统中的参数为准。
 
图E.40	返回数据结构图
表E.33	返回数据说明
字段	名称	数据类型	描述
status	状态	int	0成功 1失败
message	返回信息	String	对status的描述

E.5.3.3　巡检控制
巡检过程中，局级/段级辅助监控系统主站可以控制辅助监控系统的巡检状态，包括：启动、暂停、继续和终止，处理流程如图E.41所示。
 
图E.41	巡检控制接口图
上传巡检进度接口应满足以下要求：
a)	数据方向：段级辅助监控区  ->  辅助监控系统；
b)	接口提供方：辅助监控系统；
c)	接口路径：https://ip:port/项目路径/api/controlTask；
d)	接口说明：采用HTTPS协议，POST方式提交；
e)	数据格式：数据交换格式，body采用JSON字符串，header 需包含token字符串；
f)	有关请求数据结构如图E.42、巡检计划信息如表E.34、返回数据结构如图E.43、返回数据说明如表E.35所示。
 
图E.42	请求数据结构图
表E.34	巡检控制信息
字段	名称	数据类型	描述
name	巡检计划名称	String	
code	巡检计划编号	String	以此作为巡检计划的唯一标识
opt	巡检状态控制	String	启动start、暂停pause、继续continue、终止end

 
图E.43	返回数据结构图





表E.35	返回数据说明
字段	名称	数据类型	描述
status	状态	int	0成功 1失败
message	返回信息	String	对status的描述

E.5.3.4　上传巡检进度
辅助监控系统在执行巡检过程中，根据巡检任务的主站类型，实时向局级/段级辅助监控区上传巡检进度，处理流程如图E.44所示。
 
图E.44	上报巡检进度接口图
上报巡检进度接口应满足以下要求：
a)	数据方向：辅助监控系统  ->  局级/段级辅助监控区；
b)	接口提供方：局级/段级辅助监控区；
c)	接口路径：https://ip:port/项目路径/api/uploadTaskProgress；
d)	接口说明：采用HTTPS协议，POST方式提交；
e)	数据格式：数据交换格式，body采用JSON字符串，header 需包含token字符串；
f)	有关请求数据结构如图E.45、巡检点位信息如表E.36、返回数据结构如图E.46、返回数据说明如表E.37所示。
 
图E.45	请求数据结构图







表E.36	巡检进度信息
字段	名称	数据类型	描述
code	巡检任务编号	String	以此作为巡检任务的唯一标识
seq	巡检序号	String	如1、2、3
subName	所亭名称	String	如：XX变电所
camName	摄像机名称	String	如：进线区_球机_01
presetCode	预置位编号	String	如：2

 
图E.46	返回数据结构图

表E.37	返回数据说明
字段	名称	数据类型	描述
status	状态	int	0成功 1失败
message	返回信息	String	对status的描述
E.5.3.5　上传巡检结果
辅助监控系统巡检完成后，根据巡检任务的主站类型，将巡检照片及巡检结果（包含红外热成像的测温结果）上送局级/段级辅助监控区。辅助监控系统自身应具备重传机制，在向段级辅助监控区上送巡检结果过程中，若与段级辅助监控区网络中断或段级辅助监控区返回失败，当网络恢复后，辅助监控系统应将数据再次上送。处理流程如图E.47所示。
 
图E.47	上传巡检结果接口图
上传巡检结果接口应满足以下要求：
a)	数据方向：辅助监控系统  ->  局级/段级辅助监控区；
b)	接口提供方：局级/段级辅助监控区；
c)	接口路径：https://ip:port/项目路径/api/uploadTaskResult；
d)	接口说明：采用HTTPS协议，POST方式提交；
e)	数据格式：数据交换格式，文件使用“multipart/form-data”的格式上传，header 需包含token字符串；
f)	当巡检结果数据量较大时，应分批上传，且每包数据最大不应超过256MB。分包可分得足够小，如一个巡检点位为一包或多个巡检点位为一包；
g)	分批上传时，每一包都应包含巡检计划信息，且中间包的“endTime”字段应为空值，最后包的“endTime”字段不应为空值；
h)	分批上传时，应在巡检过程中按时间先后顺序串行上传各分包数据；
i)	有关请求数据结构如图E.48、巡检计划信息、巡检点位信息如表E.38~表E.39、返回数据结构图E.49、返回数据说明如表E.40所示。
 
图E.48	请求数据结构图
 
图F.48（续）




表E.38	巡检计划信息
字段	名称	数据类型	描述
name	巡检计划名称	String	如：日常巡检
code	巡检计划编号	String	以此作为巡检计划的唯一标识
subName	所亭名称	String	如：XX变电所
startTime	任务开始时间	String	格式：yyyy-MM-dd HH:mm:ss
endTime	任务完成时间	String	格式：yyyy-MM-dd HH:mm:ss
中间包应为空值，最后包应有值
表E.39	巡检点位信息
字段	名称	数据类型	描述
seq	巡检序号	String	如1、2、3
camName	摄像机名称	String	如：进线区_东侧龙门柱_球机
presetCode	预置位编号	String	如：2
pictureNum	照片数量	String	实际的照片数量，如5
captureTime	拍照时间	String	格式：yyyy-MM-dd HH:mm:ss
pics	文件	File[]	文件个数与实际的照片数量一致，计数从0开始。
result	执行结果	String	红外测温及图像识别的结果。格式为：“识别项名称:识别结果”，单张照片的多个识别结果以逗号分隔，多张照片的结果以分号分隔。
如：“分合状态:分”
或：“Ia:20A,Ib:19A”
status	执行状态	String	0成功 1失败
message	结果描述	String	对result的描述

 
图E.49	返回数据结构图

表E.40	返回数据说明
字段	名称	数据类型	描述
status	状态	int	0成功 1失败
message	返回信息	String	对status的描述

E.5.3.6　召唤巡检结果
若网络中断等原因导致辅助监控系统无法上传巡检结果，网络恢复后，局级/段级辅助监控区通过辅助监控系统提供的接口通知辅助监控系统上送未上传的巡检结果，辅助监控系统按E5.3.5的接口上传巡检结果。处理流程如图E.50所示。
 
图E.50	召唤未上传的巡检结果接口图
召唤未上传的巡检结果接口应满足以下要求：
a)	数据方向：局级/段级辅助监控区  ->  辅助监控系统；
b)	接口提供方：辅助监控系统；
c)	接口路径：https://ip:port/项目路径/api/callTaskResult；
d)	接口说明：采用HTTPS协议，POST方式提交；
e)	数据格式：数据交换格式，body采用JSON字符串，header 需包含token字符串；
f)	请求数据结构：无；
g)	请求数据说明：无；
h)	上传巡检结果时，分包规则与E.5.3.5　中的要求相同；
i)	有关返回数据结构如图E.51、返回数据说明如表E.41所示。

 
图E.51	返回数据结构图

表E.41	返回数据说明
字段	名称	数据类型	描述
status	状态	int	0成功 1失败
message	返回信息	String	对status的描述
E.6　图像智能校核接口
E.6.1　辅助监控系统主站与辅助监控系统的图像智能校核接口
E.6.1.1　请求开关变位智能校核
断路器和隔离开关变位后，辅助监控系统主站向辅助监控系统请求开关变位智能校核，处理流程如图E.52所示。
 
图E.52	发送开关变位校核请求
发送开关变位校核请求接口应满足以下要求：
a)	数据方向：辅助监控系统主站  ->  辅助监控系统；
b)	接口提供方：辅助监控系统；
c)	接口路径：https://ip:port/项目路径/api/pscada/checkRequest；
d)	接口说明：采用HTTPS协议，POST方式提交；
e)	数据格式：数据交换格式，body采用JSON字符串，header 需包含token字符串；
f)	有关请求数据结构如图E.53、请求数据说明如表E.42、返回数据结构如图E.54、返回数据说明如表E.43所示。
 
图E.53	请求数据结构
表E.42	请求数据说明
字段	名称	数据类型	描述
subName	所亭名称	String	如：XX变电所
equipCode	运行编号	String	供电设备的运行编号，如：211
camName	摄像机名称	String	如：进线区_东侧龙门柱_球机
presetCode	预置位编号	String	如：2
soeTime	变位时间	Long	遥信变位时间，如：1619334895970

 
图E.54	返回数据结构图
表E.43	返回数据说明
字段	名称	数据类型	描述
status	状态	int	0成功 1失败
message	返回信息	String	对status的描述
E.6.1.2　接收开关变位智能校核结果
辅助监控主站接收辅助监控系统的开关变位智能校核结果，处理流程如图E.55所示。
 
图E.55	接收开关变位校核结果
接收开关变位校核结果接口应满足以下要求：
a)	数据方向：辅助监控系统  ->  辅助监控系统主站；
b)	接口提供方：辅助监控系统主站；
c)	接口路径：https://ip:port/项目路径/api/pscada/checkResult；
d)	接口说明：采用HTTPS协议，POST方式提交；
e)	数据格式：数据交换格式，body采用JSON字符串，header 需包含token字符串；
f)	有关请求数据结构如图E.56、请求数据说明如表E.44、返回数据结构如图E.57、返回数据说明表E.45所示。
 
图E.56	请求数据结构

表E.44	请求数据说明
字段	名称	数据类型	描述
subName	所亭名称	String	如：XX变电所
equipCode	运行编号	String	供电设备运行编号，如：211
camName	摄像机名称	String	如：进线区_东侧龙门柱_球机
presetCode	预置位编号	String	如：2
soeTime	变位时间	Long	遥信变位时间，如：1619334895970
checkValue	校核值	String	图像智能识别结果，0代表分，1代表合，2代表接地，3代表未知，4代表异常

 
图E.57	返回数据结构图
表E.45	返回数据说明
字段	名称	数据类型	描述
status	状态	int	0成功 1失败
message	返回信息	String	对status的描述
E.7　摄像机鉴权接口
E.7.1　辅助监控系统主站、辅助监控系统与摄像机鉴权中心的接口
E.7.1.1　摄像机控制权限级别定义
辅助监控系统摄像机控制权限的优先级划分如表E.46所示：












表E.46	辅助监控系统摄像机控制权限的优先级定义表
权限
等级	控制或触发条件	编码	控制权
申请层级	说明
第一级	所亭内火灾报警、SF6泄漏及入侵信号视频联动	HZBJ
SF6
RQXH	所亭	牵引变电所辅助监控系统报警联动（安全相关的报警信号）
第二级	故障跳闸信号视频联动	GZTZ	局集团公司	SCADA报警联动
第三级	开关非远动动作及重要故障遥信信号视频联动（例：PT断线、综自系统远动通信异常等信号）	KGFYD
ZYGZ	局集团公司	SCADA报警联动
第四级	遥控操作视频联动	YKCZ	局集团公司	SCADA遥控联动
第五级	国铁集团用户手动调取视频画面	GTDY	国铁集团	国铁集团手动控制
第六级	局级用户手动调取视频画面	JJDY	局集团公司	局级手动控制
第七级	段级用户手动调取视频画面	DJDY	供电段	段级手动控制
第八级	车间级用户手动调取视频画面	CJDY	车间	车间级手动控制
第九级	所亭内动环及安防普通报警信号视频联动
（例：水浸传感器报警、玻璃破碎等信号）	PTXH	所亭	牵引变电所辅助监控系统报警联动（一般信号）
第十级	一般遥信故障信号视频联动
（例：主变油位异常、主变温度过高、断路器弹簧未储能、SF6压力低等信号、保护装置通信异常等信号）	YBGZ	局集团公司	SCADA报警联动
第十一级	所亭内用户手动调取视频画面	SJDY	所亭	所级手动控制
第十二级	所亭内视频巡检	SJXJ	所亭	牵引变电所辅助监控系统的视频巡检
E.7.1.2　申请摄像机控制权
辅助监控主站和辅助监控系统控制摄像机前应从鉴权中心申请摄像机的操作权，支持批量申请摄像机的操作权。通过调用鉴权中心提供的接口申请摄像机的控制权。申请摄像机控制权接口申请摄像机控制权的处理流程如图E.58所示。
 
图E.58	申请摄像机控制权
申请摄像机控制权接口应满足以下要求：
a)	数据方向： 辅助监控系统主站/辅助监控系统 -> 鉴权中心 ；
b)	接口提供方：鉴权中心；
c)	接口路径：https://ip:port/项目路径/api/cameraAuth/apply；
d)	接口说明：采用HTTPS协议，POST方式提交；
e)	数据格式：数据交换格式，body采用JSON字符串，header 需包含token字符串；
f)	当申请时摄像机控制权未被占用，可直接获得控制权。；
g)	当申请的控制权级别高于正在被占用的使用级别，可直接获得控制权。；
h)	当申请的控制权级别低于正在被占用的使用级别，申请控制权失败，鉴权中心返回预计释放的时间。；
i)	同一申请方申请摄像机控制权，获得控制权的有效时间默认为2分钟min；
j)	有关请求数据结构如图E.59、请求数据说明如表E.47、返回数据结构如图E.60、返回数据说明表E.48所示。
 
图E.59	请求数据结构
表E.47	请求数据说明
字段	名称	数据类型	描述
subName	所亭名称	String	如：XX变电所
camNames	摄像机名称列表	Array	如：[进线区_东侧龙门柱_球机]
code	控制或触发条件对应编码	String	与摄像机控制权限级别定义中的编码一致，如： HZBJ

 
图E.60	返回结果结构

表E.48	返回数据说明
字段	名称	数据类型	描述
status	状态	int	0成功 1失败
message	返回信息	String	对status的描述 
data	返回数据	Object	
  fail	申请失败的摄像机列表	Array	
camName	摄像机名称	String	摄像机名称
ttl	预计释放时间	Int	预计释放时间，单位秒
desc	申请失败的详细描述	String	
  success	申请成功的摄像机列表	Array	

E.7.1.3　释放摄像机控制权
摄像机的控制权使用完毕后应主动释放摄像机的控制权，支持批量释放摄像机的控制权接口。若不主动释放摄像机的控制权，则有效时间过期后，鉴权中心将自动释放。处理流程如图E.61所示。
 
图E.61	释放摄像机控制权
a)	数据方向：辅助监控主站/辅助监控系统  ->  鉴权中心；
b)	接口提供方：鉴权中心；
c)	接口路径：https://ip:port/项目路径/api/cameraAuth/free;；
d)	接口说明：采用HTTPS协议，POST方式提交；
e)	数据格式：数据交换格式，body采用JSON字符串，header 需包含token字符串；
f)	有关请求数据结构如图E.62、请求数据说明如表E.49、返回数据结构如图E.63、返回数据说明表E.50所示。
 
图E.62	请求数据结构
表E.49	请求数据说明
字段	名称	数据类型	描述
subName	所亭名称	String	如：XX变电所
camNames	摄像机名称列表	Array	如：[进线区_东侧龙门柱_球机]
code	控制或触发条件对应编码	String	与摄像机控制权限级别定义中的编码一致，如：HZBJ
 
图E.63	返回数据结构图



表E.50	返回数据说明
字段	名称	数据类型	描述
status	状态	int	0成功 1失败
message	返回信息	String	对status的描述
E.7.1.4　摄像机控制权变更通知
当被控站巡检申请的摄像机控制权发生变更时，鉴权中心主动通知被控站，处理流程如图E.64所示。
 
图E.64	摄像机控制权变更通知
a)	数据方向：鉴权中心 ->辅助监控系统；
b)	接口提供方：辅助监控系统；
c)	接口路径：https://ip:port/项目路径/api/cameraAuth/notice;；
d)	接口说明：采用HTTPS协议，POST方式提交；
e)	数据格式：数据交换格式，body采用JSON字符串，header 需包含token字符串；
f)	有关请求数据结构如图E.65、请求数据说明如表E.51、返回数据结构如图E.66、返回数据说明如表E.52所示。
 
图E.65	请求数据结构







表E.51	请求数据说明
字段	名称	数据类型	描述
subName	所亭名称	String	如：XX变电所
idleCamNames	空闲的摄像机名称列表	Array	如：[进线区_东侧龙门柱_球机]。
occupyCamNames	已占用的摄像机名称列表	Array	如：[进线区_东侧龙门柱_球机]。
 
图E.66	返回数据结构图
表E.52	返回数据说明
字段	名称	数据类型	描述
status	状态	int	0成功 1失败
message	返回信息	String	对status的描述

E.8　时钟同步接口
	辅助监控系统接收铁路供电调度控制系统主站的时钟同步信息，采用NTP协议或DL/T 634.5104通信协议。
